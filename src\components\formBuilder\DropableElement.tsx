import { useDroppable } from '@dnd-kit/core';
import { Box } from '@mantine/core';
import { type DropableElementType } from './Types';
import React from 'react';
import { FormBuilderElementTypeEnum } from './Constants';

const DropableElement = ({
  children,
  id,
  type,
  withBorder = true,
}: {
  children: React.ReactNode;
  id: string;
  type: DropableElementType;
  withBorder?: boolean;
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: {
      type,
    },
  });
  return (
    <Box
      ref={setNodeRef}
      style={{
        backgroundColor: isOver ? 'var(--mantine-color-blue-0)' : 'white',
        border: withBorder ? `1px ${isOver ? 'dashed' : 'solid'} ${isOver ? 'var(--mantine-color-blue-2)' : 'var(--mantine-color-gray-3)'}` : 'none',
        height: type === FormBuilderElementTypeEnum.SESSION ? 'auto' : '100%',
        borderRadius: withBorder ? 'var(--mantine-radius-md)' : 'none',
      }}
      p='lg'>
      {children}
    </Box>
  );
};

export default DropableElement;
