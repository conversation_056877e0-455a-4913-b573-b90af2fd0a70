export enum LayoutItemType {
  SESSION = 'session',
  INPUT = 'input',
  TEXT = 'text',
  BUTTON = 'button',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  TEXTAREA = 'textarea',
}

export enum SessionLayoutType {
  ONE_COLUMN = '1col',
  TWO_COLUMN = '2col',
  THREE_COLUMN = '3col',
}

export interface BaseLayoutItem {
  id: string;
  type: LayoutItemType;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export interface SessionLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.SESSION;
  layoutType: SessionLayoutType;
  children: LayoutItem[][];
  title?: string;
}

export interface InputLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.INPUT;
  inputType?: 'text' | 'email' | 'password' | 'number';
  maxLength?: number;
}

export interface TextLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.TEXT;
  content: string;
  fontSize?: string;
  fontWeight?: string;
  color?: string;
}

export interface ButtonLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.BUTTON;
  variant?: 'filled' | 'outline' | 'light';
  color?: string;
  onClick?: () => void;
}

export interface SelectLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.SELECT;
  options: { value: string; label: string }[];
  multiple?: boolean;
}

export interface CheckboxLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.CHECKBOX;
  checked?: boolean;
}

export interface TextareaLayoutItem extends BaseLayoutItem {
  type: LayoutItemType.TEXTAREA;
  rows?: number;
  maxLength?: number;
}

export type LayoutItem =
  | SessionLayoutItem
  | InputLayoutItem
  | TextLayoutItem
  | ButtonLayoutItem
  | SelectLayoutItem
  | CheckboxLayoutItem
  | TextareaLayoutItem;

export interface LayoutBuilderState {
  items: LayoutItem[];
  selectedItem?: string;
}

export interface DragData {
  type: 'new-item' | 'existing-item';
  itemType?: LayoutItemType;
  item?: LayoutItem;
  sourceIndex?: number;
  sourceSessionId?: string;
  sourceColumnIndex?: number;
}
