import React, { useState, useCallback } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCorners } from '@dnd-kit/core';
import { Grid, Stack, Title, Paper } from '@mantine/core';
import { LayoutItem, LayoutItemType, SessionLayoutItem, DragData } from './Types';
import { createNewItem, removeItemById } from './Utils';
import ItemPalette from './ItemPalette';
import DropZone from './DropZone';
import LayoutItemRenderer from './LayoutItemRenderer';

const LayoutBuilder: React.FC = () => {
  const [items, setItems] = useState<LayoutItem[]>([]);
  const [activeItem, setActiveItem] = useState<LayoutItem | null>(null);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const dragData = active.data.current as DragData;

    console.info('Drag start:', dragData); // Debug log

    if (dragData?.item) {
      setActiveItem(dragData.item);
    } else if (dragData?.itemType) {
      // Create a preview item for new items
      const previewItem = createNewItem(dragData.itemType);
      setActiveItem(previewItem);
    }
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveItem(null);

    console.info('Drag end:', {
      activeId: active.id,
      overId: over?.id,
      activeData: active.data.current,
    }); // Debug log

    if (!over) {
      console.info('No drop target');
      return;
    }

    const dragData = active.data.current as DragData;
    const overId = over.id as string;

    console.info('Drop data:', {
      dragData,
      overId,
      hasData: !!dragData,
      dragType: dragData?.type,
      itemType: dragData?.itemType,
    }); // Debug log

    // Check if dragData exists
    if (!dragData) {
      console.info('No drag data found');
      return;
    }

    // Handle dropping on main canvas
    if (overId === 'main-canvas') {
      if (dragData.type === 'new-item' && dragData.itemType) {
        console.info('Creating new item:', dragData.itemType);
        const newItem = createNewItem(dragData.itemType);
        setItems((prev) => {
          const updated = [...prev, newItem];
          console.info('Updated items:', updated);
          return updated;
        });
      } else if (dragData.type === 'existing-item' && dragData.item) {
        console.info('Moving existing item to main canvas');
        // Handle moving existing items if needed
      }
      return;
    }

    // Handle dropping on session columns
    if (overId.includes('-column-')) {
      const parts = overId.split('-column-');
      const sessionId = parts[0];
      const columnIndex = parseInt(parts[1]);

      console.info('Dropping in session:', { sessionId, columnIndex });

      if (dragData.type === 'new-item' && dragData.itemType) {
        const newItem = createNewItem(dragData.itemType);

        setItems((prev) =>
          prev.map((item) => {
            if (item.id === sessionId && item.type === LayoutItemType.SESSION) {
              const sessionItem = item as SessionLayoutItem;
              const newChildren = [...sessionItem.children];
              if (newChildren[columnIndex]) {
                newChildren[columnIndex] = [...newChildren[columnIndex], newItem];
              }
              console.info('Updated session children:', newChildren);
              return { ...sessionItem, children: newChildren };
            }
            return item;
          }),
        );
      }
    }
  }, []);
  const updateSession = useCallback((updatedSession: SessionLayoutItem) => {
    setItems((prev) => prev.map((item) => (item.id === updatedSession.id ? updatedSession : item)));
  }, []);

  const deleteSession = useCallback((sessionId: string) => {
    setItems((prev) => removeItemById(prev, sessionId));
  }, []);

  const updateItem = useCallback((updatedItem: LayoutItem) => {
    console.info('Update item:', updatedItem);
  }, []);

  const deleteItem = useCallback((itemId: string) => {
    setItems((prev) => removeItemById(prev, itemId));
  }, []);

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd} collisionDetection={closestCorners}>
      <Grid>
        {/* Item Palette */}
        <Grid.Col span={3}>
          <Paper p='md' withBorder h='100vh' style={{ overflow: 'auto' }}>
            <ItemPalette />
          </Paper>
        </Grid.Col>

        {/* Main Canvas */}
        <Grid.Col span={9}>
          <Paper p='md' withBorder h='100vh' style={{ overflow: 'auto' }}>
            <Title order={3} mb='md'>
              Layout Builder
            </Title>

            <DropZone id='main-canvas' isEmpty={items.length === 0} minHeight={500}>
              <Stack gap='md'>
                {items.map((item) => (
                  <LayoutItemRenderer
                    key={item.id}
                    item={item}
                    onUpdateItem={updateItem}
                    onDeleteItem={deleteItem}
                    onUpdateSession={updateSession}
                    onDeleteSession={deleteSession}
                  />
                ))}
              </Stack>
            </DropZone>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeItem ? (
          <Paper p='sm' withBorder shadow='lg' style={{ opacity: 0.8 }}>
            <span>{activeItem.label || activeItem.type}</span>
          </Paper>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

export default LayoutBuilder;
