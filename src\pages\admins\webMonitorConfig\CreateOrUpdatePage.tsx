import React, { useEffect, useMemo } from 'react';
import { Kanban<PERSON>itle, KanbanAccordionData, KanbanAccordion, KanbanButton } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Flex, Title } from '@mantine/core';
import GuardComponent from '@components/GuardComponent';
import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import { IconArrowBack, IconUpload } from '@tabler/icons-react';
import BaseInformationSession from './BaseInformationSession';
import classes from './CreateOrUpdatePage.module.css';
import { DEFAULT_ALERT_PRIORITY_CONFIG_ID } from '@common/constants/AlertPriorityConfigConstants';
import { ActionTypeEnum, BrowserEnum, FindElementByEnum, MonitorTypeEnum, WebMonitorConfigAction } from '@common/constants/WebMonitorConfigConstant';
import { WebMonitorConfigModel, WebMonitorConfigModelSchema } from '@models/WebMonitorConfigModel';
import { WebMonitorConfigApi } from '@api/WebMonitorConfigApi';
import AuthenticationSession from './AuthenticationSession';
import AlertSession from './AlertSession';
import IntervalSession from './IntervalSession';
import ActionSession from './ActionSession';

export const defaultAction = {
  actionType: ActionTypeEnum.CLICK,
  findElementBy: FindElementByEnum.XPATH,
  identifier: '',
  value: '',
};

const defaultValues: WebMonitorConfigModel = {
  id: '',
  name: '',
  webUrl: '',
  monitorType: MonitorTypeEnum.DISCOVER,
  timeout: 5,
  browser: BrowserEnum.CHROME,
  content: '',
  contentJson: '',
  active: true,
  serviceId: '',
  applicationId: '',
  priorityId: DEFAULT_ALERT_PRIORITY_CONFIG_ID,
  serviceName: '',
  applicationName: '',
  description: '',
  contact: '',
  months: [],
  dayOfMonths: [],
  dayOfWeeks: [],
  hours: [],
  authActions: [],
  actions: [defaultAction],
};

const TITLE_MAPS: Record<WebMonitorConfigAction, string> = {
  VIEW: 'Detail Web Monitor Config',
  UPDATE: 'Update Web Monitor Config',
  CREATE: 'Create Web Monitor Config',
  DELETE: 'Delete Web Monitor Config',
};

const CreateOrUpdateWebMonitorConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const { id = '' } = useParams<{ id?: string }>();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action') || WebMonitorConfigAction.CREATE;
  const isEdit = Boolean(id);
  const isView = action === WebMonitorConfigAction.VIEW;

  const form = useForm<WebMonitorConfigModel>({
    defaultValues,
    resolver: zodResolver(WebMonitorConfigModelSchema),
    mode: 'onChange',
  });

  const monitorType = useWatch({
    control: form.control,
    name: 'monitorType',
  });

  const {
    formState: { isSubmitting, isValid },
    handleSubmit,
    reset,
  } = form;

  const { data: detail, isFetching } = useFetch(WebMonitorConfigApi.findById(id), { enabled: isEdit });

  const { mutate: saveConfig } = useMutate(WebMonitorConfigApi.save, {
    successNotification:
      isEdit && action === WebMonitorConfigAction.UPDATE ? 'Update Web Monitor Config Successfully' : 'Create Web Monitor Config Successfully',
    onSuccess: () => navigate(ROUTE_PATH.WEB_MONITOR_CONFIG),
  });

  // Populate form when editing
  useEffect(() => {
    if (detail?.data && !isFetching) {
      reset({
        ...detail.data,
        id: action === WebMonitorConfigAction.UPDATE ? detail.data.id : undefined,
      });
    }
  }, [action, detail, isFetching, reset]);

  const onSave = handleSubmit((data) => {
    saveConfig(data);
  });

  const accordionItems: KanbanAccordionData[] = useMemo(() => {
    const items: KanbanAccordionData[] = [
      {
        key: 'general',
        title: <KanbanTitle order={4}>General Information</KanbanTitle>,
        content: <BaseInformationSession form={form} isViewMode={isView} />,
      },
    ];

    if (monitorType === MonitorTypeEnum.LOGIN) {
      items.push({
        key: 'authentication',
        title: <KanbanTitle order={4}>Authentication</KanbanTitle>,
        content: <AuthenticationSession form={form} isViewMode={isView} />,
      });
    }

    items.push({
      key: 'interval',
      title: <KanbanTitle order={4}>Interval</KanbanTitle>,
      content: <IntervalSession form={form} isViewMode={isView} />,
    });

    items.push({
      key: 'action',
      title: <KanbanTitle order={4}>Action</KanbanTitle>,
      content: <ActionSession form={form} isViewMode={isView} />,
    });

    items.push({
      key: 'alert',
      title: <KanbanTitle order={4}>Alert</KanbanTitle>,
      content: <AlertSession form={form} isViewMode={isView} />,
    });
    return items;
  }, [form, isView, monitorType]);
  return (
    <>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>{TITLE_MAPS[action as WebMonitorConfigAction]}</Title>
        <Flex gap='sm'>
          <KanbanButton leftSection={<IconArrowBack />} variant='outline' onClick={() => navigate(ROUTE_PATH.WEB_MONITOR_CONFIG)}>
            Cancel
          </KanbanButton>
          {!isView && (
            <GuardComponent requirePermissions={[AclPermission.webMonitorConfigCreate, AclPermission.webMonitorConfigEdit]}>
              <KanbanButton leftSection={<IconUpload />} onClick={onSave} disabled={!isValid || isSubmitting}>
                Save
              </KanbanButton>
            </GuardComponent>
          )}
        </Flex>
      </Flex>

      <KanbanAccordion data={accordionItems} defaultValue={accordionItems.map((item) => String(item.key))} />
    </>
  );
};

export default CreateOrUpdateWebMonitorConfigPage;
