import React from 'react';
import { Checkbox, NumberInput, Stack, TextInput } from '@mantine/core';
import { NumberInputElement } from '../Types';
import { Control, Controller } from 'react-hook-form';

interface Props {
  control: Control<NumberInputElement>;
}

const NumberSettingForm = ({ control }: Props) => {
  return (
    <Stack gap='md'>
      <Controller name='label' control={control} render={({ field }) => <TextInput label='Label' {...field} />} />
      <Controller name='defaultValue' control={control} render={({ field }) => <NumberInput label='Default value' {...field} />} />
      <Controller name='helpText' control={control} render={({ field }) => <TextInput label='Help text' {...field} />} />
      <Controller name='placeholder' control={control} render={({ field }) => <TextInput label='Placeholder' {...field} />} />
      <Controller name='min' control={control} render={({ field }) => <NumberInput label='Min' {...field} />} />
      <Controller name='max' control={control} render={({ field }) => <NumberInput label='Max' {...field} />} />
      <Controller
        name='required'
        control={control}
        render={({ field }) => <Checkbox label='Required' checked={field.value} onChange={field.onChange} />}
      />
    </Stack>
  );
};

export default NumberSettingForm;
