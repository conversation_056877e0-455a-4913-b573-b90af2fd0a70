import React from 'react';
import { SessionElement } from '../Types';
import { Control, Controller } from 'react-hook-form';
import { SessionLayoutEnum, SessionLayoutInfo } from '../Constants';
import { ComboboxData, Stack, TextInput } from '@mantine/core';
import { KanbanSelect } from 'kanban-design-system';

interface Props {
  control: Control<SessionElement>;
}

const LayoutOptions: ComboboxData = Object.values(SessionLayoutEnum).map((key) => ({
  value: key,
  label: SessionLayoutInfo[key].label,
}));

const SessionSettingForm = ({ control }: Props) => {
  return (
    <Stack gap='md'>
      <Controller name='label' control={control} render={({ field }) => <TextInput label='Label' {...field} />} />
      <Controller
        name='layout'
        control={control}
        render={({ field }) => <KanbanSelect allowDeselect={false} label='Layout' data={LayoutOptions} {...field} />}
      />
    </Stack>
  );
};

export default SessionSettingForm;
