import { BaseURL } from '@common/constants/BaseUrl';
import { createCursorPageSchema, createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { createRequest } from './Utils';
import { NotificationSearchModel } from '@models/NotificationSearchModel';
import { NotificationSchema } from '@core/schema/Notification';
import { NotificationCursor, NotificationCursorSchema } from '@core/schema/NotificationCursor';
import { CursorPagingRequestModel } from '@models/CursorPagingRequestModel';

export class NotificationApi {
  static getUnreadCount() {
    return createRequest({
      url: `${BaseURL.notification}/unread-count`,
      method: 'GET',
      schema: createResponseSchema(z.number()),
    });
  }

  static markAsRead(id: string) {
    return createRequest<string>({
      url: `${BaseURL.notification}/:id/mark-as-read`,
      method: 'PUT',
      pathVariable: {
        id,
      },
    });
  }

  static findAll(cursor: CursorPagingRequestModel<NotificationCursor>, searchRequest?: NotificationSearchModel) {
    return createRequest({
      url: `${BaseURL.notification}`,
      method: 'GET',
      schema: createResponseSchema(createCursorPageSchema(NotificationSchema, NotificationCursorSchema)),
      params: { ...searchRequest, ...cursor },
    });
  }
}
