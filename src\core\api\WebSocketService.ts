import { NotificationApi } from '@api/NotificationApi';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { NotificationError } from '@common/utils/NotificationUtils';
import { refetchRequest } from '@common/utils/QueryUtils';
import { Notification, NotificationSchema } from '@core/schema/Notification';
import { Client } from '@stomp/stompjs';
import type { IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export class WebSocketService {
  private client: Client | null = null;
  private isConnected = false;

  constructor() {
    console.info('Initializing WebSocket service...');
    this.initializeClient();
  }

  private initializeClient() {
    const wsUrl = 'http://localhost:9000/api/external-notification/ws';
    console.info('WebSocket URL:', wsUrl);

    const token = localStorage.getItem(LocalStorageKey.MONITOR_ACCESS_TOKEN);
    if (!token) {
      console.error('No authentication token found');
      return;
    }

    this.client = new Client({
      webSocketFactory: () => new SockJS(wsUrl),
      connectHeaders: {
        Authorization: `Bearer ${token}`,
      },
      debug: (str: string) => {
        console.info('STOMP Debug:', str);
      },
      reconnectDelay: 5000, // automatic reconnect every 5s
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.client) {
      return;
    }

    this.client.onConnect = () => {
      console.info('WebSocket Connected Successfully');
      this.isConnected = true;
      this.subscribeToNotifications();
    };

    this.client.onStompError = (frame: { headers: Record<string, string>; body: string }) => {
      if (frame.body.includes('401')) {
        console.error('STOMP Error: Authentication failed', frame);
      } else {
        console.error('STOMP Error: Failed to connect to notification service', frame);
      }
    };

    this.client.onWebSocketError = (event: Event) => {
      console.error('WebSocket Error:', event);
    };

    this.client.onWebSocketClose = () => {
      console.info('WebSocket Connection Closed');
      this.isConnected = false;
    };
  }

  private subscribeToNotifications() {
    if (!this.client?.connected) {
      console.error('Cannot subscribe: WebSocket not connected');
      return;
    }

    console.info('Subscribing to notification channels...');

    this.client.subscribe('/user/queue/notifications', this.handleNotification);
    this.client.subscribe('/topic/notifications', this.handleNotification);

    console.info('Successfully subscribed to notification channels');
  }

  private handleNotification(message: IMessage) {
    console.info('Received broadcast notification:', message);
    const notification = NotificationSchema.safeParse(JSON.parse(message.body));
    if (!notification.success) {
      console.error('Failed to parse notification:', notification.error);
      return;
    }

    NotificationError({
      title: notification.data.title,
      message: notification.data.content,
    });

    refetchRequest(NotificationApi.getUnreadCount());
  }

  public connect() {
    console.info('Attempting to connect to WebSocket...');
    if (!this.client) {
      console.error('WebSocket client not initialized');
      return;
    }

    try {
      this.client.activate();
      console.info('WebSocket activation initiated');
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  }

  public disconnect() {
    console.info('Disconnecting from WebSocket...');
    if (!this.client) {
      console.error('WebSocket client not initialized');
      return;
    }

    try {
      this.client.deactivate();
      this.isConnected = false;
      console.info('WebSocket disconnected successfully');
    } catch (error) {
      console.error('Failed to disconnect from WebSocket:', error);
    }
  }

  public isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  public sendNotification(notification: Omit<Notification, 'id' | 'read' | 'createdAt'>) {
    if (!this.client?.connected) {
      console.error('Cannot send notification: WebSocket not connected');
      NotificationError({
        title: 'WebSocket Error',
        message: 'Not connected to notification service',
      });
      return;
    }

    console.info('Sending notification:', notification);
    this.client.publish({
      destination: '/app/send',
      body: JSON.stringify(notification),
    });
  }
}
