export const NAME_MAX_LENGTH: number = 100;
export const DESCRIPTION_MAX_LENGTH: number = 300;

export const QUERY_SQL_NAME_MAX_LENGTH: number = 100;
export const MIN_STRING_LENGTH = 1;
export const MAX_ALERT_PRIORITY_CONFIG_NAME_LENGTH = 100;
export const MAX_ALERT_PRIORITY_CONFIG_COLOR_LENGTH = 20;
export const MAX_ALERT_PRIORITY_CONFIG_RAW_VALUE_LENGTH = 300;
export const LIMIT_EXPORT_ROWS_LENGTH = 1_000_000;
export const LIMIT_ALERT_EXPORT_ROWS_LENGTH = LIMIT_EXPORT_ROWS_LENGTH;
export const MAX_CHARACTER_FILE_NAME_LENGTH = 100;
export const MAX_CHARACTER_NAME_LENGTH = 100;
export const MAX_EMAIL_CHARACTER_LENGTH = 320;
export const MAX_EMAIL_USERNAME_LENGTH = 64;
export const MAX_EMAIL_DOMAIN_LENGTH = 255;
export const MAX_EMAIL_CONFIG_DOMAIN_LENGTH = 253;
export const MAX_EMAIL_ADDRESSES = 100;
export const MAX_EMAIL_SUBJECT_CHARACTER_LENGTH = 150;
export const MAX_DESCRIPTION_LENGTH = 300;
export const DEFAULT_DEBOUNCE_TIME = 300;
export const DATABASE_NAME_MAX_LENGTH: number = 100;
export const DATABASE_DESCRIPTION_MAX_LENGTH: number = 300;
export const DATABASE_HOST_MAX_LENGTH: number = 245;
export const DATABASE_SID_OR_SERVICE_MAX_LENGTH: number = 50;
export const DATABASE_USERNAME_MAX_LENGTH: number = 128;
export const DATABASE_PASSWORD_MAX_LENGTH: number = 128;
export const DATABASE_CONNECTION_NAME_MAX_LENGTH: number = 128;
export const DATABASE_PORT_MIN_LENGTH: number = 0;
export const DATABASE_PORT_MAX_LENGTH: number = 65535;

export const DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH: number = 30;
export const DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH: number = 50;
export const DATABASE_COLLECT_SQL_COMMAND_MAX_LENGTH: number = 65536;
export const MAX_NAME_LENGTH = 100;
export const MAX_RECIPIENT_LENGTH = 255;

export const CHARACTER_NAME_COLLECT_EMAIL_CONFIG_MAX_LENGTH = 100;
export const CHARACTER_DESCRIPTION_COLLECT_EMAIL_CONFIG_MAX_LENGTH = 300;
export const CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH = 255;
export const MAX_ALERT_GROUP_CONFIG_NAME_LENGTH = 100;
export const MAX_ALERT_GROUP_CONFIG_DESCRIPTION_LENGTH = 200;
export const CUSTOM_OBJECT_INDEX_MAX = 1000;
export const TABLE_INPUT_MAX_LENGTH = 100;
export const CHARACTER_CONTENT_ALERT_MAX_LENGTH = 3000;

export const SQL_COMMAND_MAX_LENGTH: number = 4000;

export const MAX_CRON_EXPRESSION_LENGTH = 100;

export const PARAM_QUERY_SQL_MAX_LENGTH = 300;

export const TELEGRAM_GROUP_ID_LENGTH = 30;
export const MAX_PASSWORD_LENGTH = 100;
export const MIN_PASSWORD_LENGTH = 8;

/**
 * Teams
 */
export const TEAMS_CLIENT_ID_MAX_LENGTH = 50;
export const TEAMS_CLIENT_SECRET_MAX_LENGTH = 155;
export const TEAMS_TENANT_ID_MAX_LENGTH = 100;
export const TEAMS_EMAIL_MAX_LENGTH = 255;
export const TEAMS_PASSWORD_MAX_LENGTH = 255;
export const TEAMS_MESSAGE_TEMPLATE_MAX_LENGTH = 26000;
export const TEAMS_INTERVAL_MAX_LENGTH = 2880;
export const TEAMS_MAX_CONTACTS = 100;
export const TEAMS_MESSAGE_CONTENT_MAX_LENGTH = 26000;

export const MESSAGE_SYS_LOG_MAX_LENGTH = 300;
export const EXECUTION_SCRIPT_MAX_LENGTH = 100_000;
export const VARIABLE_MAX_LENGTH = 2000;

/**
 * Trigger
 */
export const MIN_TIME_SINCE_LAST_TRIGGER = 1800;
export const MAX_TIME_SINCE_LAST_TRIGGER = 86400;
export const MAX_ENABLE_TIME_SINCE_LAST_TRIGGER = 1000000;

// RPA Config Contants
export const RPA_CONFIG_INTERVAL_MIN_VALUE = 1;
export const RPA_CONFIG_INTERVAL_MAX_VALUE = 3600;
export const RPA_CONFIG_NUMBER_OF_RETRY_MIN_VALUE = 0;
export const RPA_CONFIG_NUMBER_OF_RETRY_MAX_VALUE = 3;

// Web Monitor Contants
export const WEB_MONITOR_WEB_URL_MAX_LENGTH = 2000;
export const WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE = 5;
export const WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE = 60;

export const MAX_NOTIFICATION_EVENT_TITLE_LENGTH = 100;
export const MAX_NOTIFICATION_EVENT_CONTENT_LENGTH = 3000;
export const MAX_NOTIFICATION_EVENT_CONTENT_JSON_LENGTH = 5000;
