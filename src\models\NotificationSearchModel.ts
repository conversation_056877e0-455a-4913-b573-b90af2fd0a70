import { NotificationSourceTypeEnum, NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { NotificationCursorSchema } from '@core/schema/NotificationCursor';
import { z } from 'zod';

export const NotificationSearchModelSchema = NotificationCursorSchema.extend({
  search: z.string().optional(),
  types: z.array(z.nativeEnum(NotificationTypeEnum)).optional(),
  sourceType: z.array(z.nativeEnum(NotificationSourceTypeEnum)).optional(),
});

export type NotificationSearchModel = z.infer<typeof NotificationSearchModelSchema>;
