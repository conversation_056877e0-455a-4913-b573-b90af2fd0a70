import { ComboboxItem } from '@mantine/core';
import { FormBuilderElementTypeEnum, SessionLayoutEnum } from './Constants';

export type BaseFormBuilderElement = {
  id: string;
  type: FormBuilderElementTypeEnum;
  label: string;
};

export type SessionElement = BaseFormBuilderElement & {
  layout: SessionLayoutEnum;
  type: FormBuilderElementTypeEnum.SESSION;
  elements: BaseFormBuilderElement[];
};

export type BaseFormBuilderInputElement = BaseFormBuilderElement & {
  required: boolean;
  helpText: string;
};

export type TextInputElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.TEXT;
  placeholder: string;
  maxLength: number;
  defaultValue: string | undefined;
};

export type NumberInputElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.NUMBER;
  placeholder: string;
  defaultValue: number | undefined;
  min: number;
  max: number;
};

export type CheckboxElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.CHECKBOX;
};

export type RadioElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.RADIO;
};

export type SelectElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.SELECT;
  placeholder: string;
  options: ComboboxItem[];
  defaultValue: ComboboxItem[] | ComboboxItem | undefined;
  multipleSelect: boolean;
};

export type DateTimeElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.DATETIME;
  placeholder: string;
};

export type TimeRangeElement = BaseFormBuilderInputElement & {
  type: FormBuilderElementTypeEnum.TIME_RANGE;
  fromDatePlaceholder: string;
  toDatePlaceholder: string;
  fromDate: string;
  toDate: string;
  fromDateRequired: boolean;
  toDateRequired: boolean;
  fromDateLabel: string;
  toDateLabel: string;
};

export type FormBuilderElement =
  | TextInputElement
  | NumberInputElement
  | CheckboxElement
  | RadioElement
  | SelectElement
  | SessionElement
  | DateTimeElement
  | TimeRangeElement;

export type DropableElementType = FormBuilderElementTypeEnum.SESSION;

export type DraggableData = {
  type: DropableElementType;
  isNew: boolean;
  element: FormBuilderElement;
  currentParent?: FormBuilderElement;
};

export type DropableData = {
  type: DropableElementType;
};

export type OnDeleteElementFnc = (elementId: string, parentElementId?: string) => void;

export type OnUpdateElementFnc = (element: FormBuilderElement) => void;
