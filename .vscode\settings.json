{
  "cssVariables.lookupFiles": [
    "**/*.css",
    "**/*.scss",
    "**/*.sass",
    "**/*.less",
    "node_modules/@mantine/core/esm/index.css"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.tabSize": 2,
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "terminal.integrated.scrollback": 100000000,
  "typescript.tsdk": "node_modules\\typescript\\lib",
}