import React from 'react';
import { LayoutItem, LayoutItemType } from './Types';
import { KanbanInput, KanbanButton, KanbanSelect, KanbanCheckbox, KanbanTextarea } from 'kanban-design-system';
import { Text, Paper, Group, ActionIcon } from '@mantine/core';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import DraggableItem from './DraggableItem';
import SessionComponent from './SessionComponent';

interface LayoutItemRendererProps {
  item: LayoutItem;
  onUpdateItem?: (item: LayoutItem) => void;
  onDeleteItem?: (itemId: string) => void;
  onUpdateSession?: (session: any) => void;
  onDeleteSession?: (sessionId: string) => void;
}

const LayoutItemRenderer: React.FC<LayoutItemRendererProps> = ({ item, onDeleteItem, onDeleteSession, onUpdateItem, onUpdateSession }) => {
  const renderItemContent = () => {
    switch (item.type) {
      case LayoutItemType.SESSION:
        return (
          <SessionComponent session={item as any} onUpdateSession={onUpdateSession || (() => {})} onDeleteSession={onDeleteSession || (() => {})} />
        );

      case LayoutItemType.INPUT:
        return <KanbanInput label={item.label} placeholder={(item as any).placeholder} disabled={item.disabled} required={item.required} />;

      case LayoutItemType.TEXT:
        // eslint-disable-next-line no-case-declarations
        const textItem = item as any;
        return (
          <Text size={textItem.fontSize} fw={textItem.fontWeight} c={textItem.color}>
            {textItem.content}
          </Text>
        );

      case LayoutItemType.BUTTON:
        // eslint-disable-next-line no-case-declarations
        const buttonItem = item as any;
        return (
          <KanbanButton variant={buttonItem.variant} color={buttonItem.color} disabled={item.disabled}>
            {item.label}
          </KanbanButton>
        );

      case LayoutItemType.SELECT:
        // eslint-disable-next-line no-case-declarations
        const selectItem = item as any;
        return <KanbanSelect label={item.label} data={selectItem.options} disabled={item.disabled} required={item.required} />;

      case LayoutItemType.CHECKBOX:
        return <KanbanCheckbox label={item.label} disabled={item.disabled} />;

      case LayoutItemType.TEXTAREA:
        // eslint-disable-next-line no-case-declarations
        const textareaItem = item as any;
        return (
          <KanbanTextarea
            label={item.label}
            placeholder={textareaItem.placeholder}
            rows={textareaItem.rows}
            disabled={item.disabled}
            required={item.required}
          />
        );

      default:
        return <Text>Unknown item type</Text>;
    }
  };

  // For session items, render directly without wrapper
  if (item.type === LayoutItemType.SESSION) {
    return renderItemContent();
  }

  // For other items, wrap in draggable container
  return (
    <DraggableItem item={item}>
      <Paper p='sm' withBorder style={{ width: '100%' }}>
        <Group justify='space-between' align='flex-start'>
          <div style={{ flex: 1 }}>{renderItemContent()}</div>
          <Group gap='xs'>
            <ActionIcon size='sm' variant='subtle' onClick={() => onUpdateItem?.(item)}>
              <IconEdit size={12} />
            </ActionIcon>
            <ActionIcon size='sm' variant='subtle' color='red' onClick={() => onDeleteItem?.(item.id)}>
              <IconTrash size={12} />
            </ActionIcon>
          </Group>
        </Group>
      </Paper>
    </DraggableItem>
  );
};

export default LayoutItemRenderer;
