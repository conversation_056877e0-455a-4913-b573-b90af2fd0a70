import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Paper, Text, Group } from '@mantine/core';
import { LayoutItem } from './Types';
import { IconGripVertical } from '@tabler/icons-react';

interface DraggableItemProps {
  item: LayoutItem;
  isNew?: boolean;
  children?: React.ReactNode;
}

const DraggableItem: React.FC<DraggableItemProps> = ({ children, isNew = false, item }) => {
  // Use consistent ID - don't change it during drag
  const dragId = isNew ? `new-${item.type}-${item.id}` : item.id;

  // Create the drag data object
  const dragData = {
    type: isNew ? 'new-item' : 'existing-item',
    itemType: item.type,
    item: item,
  };

  const { attributes, isDragging, listeners, setNodeRef, transform } = useDraggable({
    id: dragId,
    data: dragData, // Pass the data directly, not as current
  });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        zIndex: 1000,
      }
    : undefined;

  return (
    <Paper
      ref={setNodeRef}
      style={style}
      p='sm'
      withBorder
      shadow={isDragging ? 'lg' : 'sm'}
      sx={{
        cursor: isDragging ? 'grabbing' : 'grab',
        opacity: isDragging ? 0.5 : 1,
        userSelect: 'none',
        '&:hover': {
          shadow: 'md',
        },
      }}
      {...attributes}
      {...listeners}>
      <Group gap='xs'>
        <IconGripVertical size={16} color='gray' />
        {children || (
          <Text size='sm' fw={500}>
            {item.label || item.type}
          </Text>
        )}
      </Group>
    </Paper>
  );
};

export default DraggableItem;
