import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Paper, Text, Group } from '@mantine/core';
import { LayoutItem } from './Types';
import { IconGripVertical } from '@tabler/icons-react';

interface DraggableItemProps {
  item: LayoutItem;
  isNew?: boolean;
  children?: React.ReactNode;
}

const DraggableItem: React.FC<DraggableItemProps> = ({ children, isNew = false, item }) => {
  const dragId = isNew ? `new-${item.type}-${Date.now()}` : item.id;

  const { attributes, isDragging, listeners, setNodeRef, transform } = useDraggable({
    id: dragId,
    data: {
      type: isNew ? 'new-item' : 'existing-item',
      itemType: item.type,
      item,
    },
  });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        zIndex: 1000,
      }
    : undefined;

  return (
    <Paper ref={setNodeRef} style={style} p='sm' withBorder shadow={isDragging ? 'lg' : 'sm'} {...attributes} {...listeners}>
      <Group gap='xs'>
        <IconGripVertical size={16} color='gray' />
        {children || (
          <Text size='sm' fw={500}>
            {item.label || item.type}
          </Text>
        )}
      </Group>
    </Paper>
  );
};

export default DraggableItem;
