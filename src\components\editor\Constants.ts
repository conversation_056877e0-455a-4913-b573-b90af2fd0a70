import { ViewUpdate } from '@uiw/react-codemirror';
import { EditorView } from 'codemirror';

export const MaxLengthExtension = (maxLength: number) =>
  EditorView.updateListener.of((update: ViewUpdate) => {
    const view = update.view;
    if (update.docChanged) {
      const content = view.state.doc.toString();
      if (content.length > maxLength) {
        // Truncate and replace content
        view.dispatch({
          changes: {
            from: 0,
            to: content.length,
            insert: content.slice(0, maxLength),
          },
        });
      }
    }
  });
