import React from 'react';
import type { RouterType } from '@components/appShell';
import DashboardPage from '@pages/dashboard';

import type { RouteProps } from 'react-router-dom';
import WebHookPage from '@pages/admins/webHook';

export const navbarConfigs: RouterType[] = [];
import ReportViewPage from '@pages/report';
import DesignSystemPage from '@pages/developGuides/designSystems';
import DemoUseContextPage from '@pages/developGuides/demoUseContext';
import GuardRoute from '@components/GuardRoute';
import DemoForbiddenPage, { DemoForbiddenComponentPage } from '@pages/developGuides/demoForbiddenPage';
import { AclPermission } from '@models/AclPermission';
import ManagementServiceAndApplicationPage from '@pages/admins/serviceApplication';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import AdminViewPage from '@pages/admins';
import UserManagementPage from '@pages/admins/users';
import MonitorAlertPage from '@pages/monitorAlert';
import PriorityConfigPage from '@pages/admins/priorityConfig';
import CustomObjectPage from '@pages/admins/customObject';
import PartnerManagement from '@pages/admins/PartnerManagement';
import EmailConfigPage from '@pages/admins/emailConfig';
import CollectEmailConfigPage from '@pages/admins/collectEmail';
import { TemplateManagement } from '@pages/admins/emailTemplate';
import CreateOrUpdatePage from '@pages/admins/emailTemplate/CreateOrUpdatePage';
import DatabaseCollectPage from '@pages/admins/databaseCollect';
import CreateOrUpdateCollectEmailConfigPage from '@pages/admins/collectEmail/CreateOrUpdatePage';
import DatabaseConnectionPage from '@pages/admins/databaseConnection';
import GroupConfigPage from '@pages/admins/groupConfig';
import GroupConfigFormPage from '@pages/admins/groupConfig/GroupConfigFormPage';
import SendEmailViewPage from '@pages/sendEmail';
import MaintenanceTimeConfigPage from '@pages/admins/maintenanceTimeConfig';
import MaintenanceTimeConfigFormPage from '@pages/admins/maintenanceTimeConfig/MaintenanceTimeConfigFormPage';
import SqlExecutionPage from '@pages/admins/superiors/SqlExecutionPage';
import EventPage from '@pages/events';
import CreateOrUpdateShiftHandoverPage from '@pages/events/createOrUpdateShiftHanoverPage';
import TelegramPage from '@pages/admins/telegram/TelegramPage';
import FilterAlertConfigPage from '@pages/admins/filterAlertConfig';
import FilterAlertConfigFormPage from '@pages/admins/filterAlertConfig/FilterAlertConfigFormPage';
import ModifyAlertConfigPage from '@pages/admins/modifyAlertConfig';
import ModifyAlertConfigFormPage from '@pages/admins/modifyAlertConfig/ModifyAlertConfigFormPage';
import SysLogPage from '@pages/admins/sysLog';
import ManagementExportDataPage from '@pages/exportData';
import TeamsPage from '@pages/admins/teams/TeamsPage';
import CreateOrUpdateDatabaseThresholdConfigPage from '@pages/admins/databaseThreshold/CreateOrUpdatePage';
import DatabaseThresholdConfigPage from '@pages/admins/databaseThreshold';
import { CollectEmailConfigAction } from '@common/constants/CollectEmailConfigConstant';
import ExecutionPage from '@pages/executions';
import ExecutionHistoryPage from '@pages/admins/executionHistory';
import ExecutionHistoryDetailPage from '@pages/admins/executionHistory/executionHistoryDetail';
import ExecutionConfigPage from '@pages/admins/executionConfig';
import VariablePage from '@pages/admins/variable';
import AutoTriggerActionConfigPage from '@pages/admins/autoTriggerActionConfig';
import AutoTriggerActionConfigFormPage from '@pages/admins/autoTriggerActionConfig/AutoTriggerActionConfigPage';
import RpaConfigPage from '@pages/admins/rpaConfig/RpaConfigPage';
import WebMonitorConfigPage from '@pages/admins/webMonitorConfig/WebMonitorConfigPage';
import CreateOrUpdateWebMonitorConfigPage from '@pages/admins/webMonitorConfig/CreateOrUpdatePage';
import NotificationEventPage from '@pages/admins/notificationEvent';
import NotificationEventDetail from '@pages/admins/notificationEvent/NotificationEventDetail';

export const headerLinkConfigs: RouterType[] = [
  {
    path: ROUTE_PATH.MONITOR_ALERT,
    name: 'Monitor Alert',
    requirePermissions: [AclPermission.monitorAlertView],
  },
  {
    path: ROUTE_PATH.REPORT,
    name: 'Report',
    requirePermissions: [AclPermission.reportView],
  },
  {
    path: ROUTE_PATH.EMAIL,
    name: 'Email',
    requirePermissions: [AclPermission.sendEmail],
  },
  {
    path: ROUTE_PATH.EVENT,
    name: 'Event',
    requirePermissions: [AclPermission.taskView],
  },
  {
    path: ROUTE_PATH.EXECUTION,
    name: 'Execution',
    requirePermissions: [AclPermission.anyExecution],
  },
  {
    path: ROUTE_PATH.EXPORT_DATA,
    name: 'Export data',
  },
  {
    path: ROUTE_PATH.ADMIN,
    name: 'Admins',
    requirePermissions: AclPermission.adminView,
  },
];
export const getHeaderLink = (): RouterType[] => {
  return headerLinkConfigs;
};

type RoutePropsOmit = Omit<RouteProps, 'children'>;
export type RoutePropsType = RoutePropsOmit & {
  children?: RoutePropsType[];
};

export const routeConfigs: RoutePropsType[] = [
  {
    path: ROUTE_PATH.EMAIL,
    element: (
      <GuardRoute requirePermissions={[AclPermission.sendEmail]}>
        <SendEmailViewPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.ADMIN,
    element: <AdminViewPage />,
    children: [
      {
        path: ROUTE_PATH.SYS_LOG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.syslogView]}>
            <SysLogPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.SUPERIORS,
        element: (
          <GuardRoute requirePermissions={[AclPermission.superAdmin]}>
            <SqlExecutionPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.TELEGRAM_ALERT_CONFIG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.telegramAlertConfig]}>
            <TelegramPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.TEAMS_ALERT_CONFIG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.teamsAlertConfig]}>
            <TeamsPage />
          </GuardRoute>
        ),
      },
      {
        index: true,
        path: ROUTE_PATH.WEBHOOK,
        element: (
          <GuardRoute requirePermissions={[AclPermission.webHookView]}>
            <WebHookPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.DATABASE_COLLECT,
        element: (
          <GuardRoute requirePermissions={[AclPermission.databaseCollectView]}>
            <DatabaseCollectPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.DATABASE_CONNECTION,
        element: (
          <GuardRoute requirePermissions={[AclPermission.databaseConnectionView]}>
            <DatabaseConnectionPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.RPA_CONFIG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.rpaConfigView]}>
            <RpaConfigPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.WEB_MONITOR_CONFIG,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.webMonitorConfigView]}>
                <WebMonitorConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: CollectEmailConfigAction.CREATE,
            element: (
              <GuardRoute requirePermissions={[AclPermission.webMonitorConfigCreate]}>
                <CreateOrUpdateWebMonitorConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute
                requirePermissions={[AclPermission.webMonitorConfigView, AclPermission.webMonitorConfigEdit, AclPermission.webMonitorConfigCreate]}>
                <CreateOrUpdateWebMonitorConfigPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.SERVICE_APPLICATION,
        element: (
          <GuardRoute requirePermissions={[AclPermission.serviceManageView, AclPermission.applicationManageView]}>
            <ManagementServiceAndApplicationPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.CUSTOM_OBJECT,
        element: (
          <GuardRoute requirePermissions={[AclPermission.customObjectView]}>
            <CustomObjectPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.EMAIL_CONNECTION,
        element: (
          <GuardRoute requirePermissions={[AclPermission.emailConnectionView]}>
            <EmailConfigPage />
          </GuardRoute>
        ),
      },

      {
        path: ROUTE_PATH.EMAIL_COLLECT,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.emailCollectView]}>
                <CollectEmailConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute requirePermissions={[AclPermission.emailCollectView, AclPermission.emailCollectEdit, AclPermission.emailCollectCreate]}>
                <CreateOrUpdateCollectEmailConfigPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.WEBHOOK,
        element: (
          <GuardRoute requirePermissions={[AclPermission.webHookView]}>
            <WebHookPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.PERMISSION_MANAGERMENT,
        element: (
          <GuardRoute requirePermissions={[AclPermission.userManageView, AclPermission.roleManageView]}>
            <UserManagementPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.PRIORITY_CONFIG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.priorityConfigView]}>
            <PriorityConfigPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.PARTNER_MANAGEMENT,
        element: (
          <GuardRoute requirePermissions={[AclPermission.emailPartnerView]}>
            <PartnerManagement />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.EMAIL_TEMPLATE,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.emailTemplateView]}>
                <TemplateManagement />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute requirePermissions={[AclPermission.emailTemplateView, AclPermission.emailTemplateEdit, AclPermission.emailTemplateCreate]}>
                <CreateOrUpdatePage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.GROUP_CONFIG,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.alertGroupConfigView]}>
                <GroupConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':groupConfigId',
            element: (
              <GuardRoute
                requirePermissions={[AclPermission.alertGroupConfigView, AclPermission.alertGroupConfigEdit, AclPermission.alertGroupConfigCreate]}>
                <GroupConfigFormPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.MAINTENANCE_TIME_CONFIG,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.maintenanceTimeConfigView]}>
                <MaintenanceTimeConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':maintenanceTimeConfigId',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.maintenanceTimeConfigView,
                  AclPermission.maintenanceTimeConfigEdit,
                  AclPermission.maintenanceTimeConfigCreate,
                ]}>
                <MaintenanceTimeConfigFormPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.FILTER_ALERT_CONFIG,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.filterAlertConfigView]}>
                <FilterAlertConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':filterAlertConfigId',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.filterAlertConfigView,
                  AclPermission.filterAlertConfigEdit,
                  AclPermission.filterAlertConfigCreate,
                ]}>
                <FilterAlertConfigFormPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.MODIFY_ALERT_CONFIG,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.modifyAlertConfigView]}>
                <ModifyAlertConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':modifyAlertConfigId',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.modifyAlertConfigView,
                  AclPermission.modifyAlertConfigEdit,
                  AclPermission.modifyAlertConfigCreate,
                ]}>
                <ModifyAlertConfigFormPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.DATABASE_THRESHOLD,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.databaseThresholdConfigView]}>
                <DatabaseThresholdConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: CollectEmailConfigAction.CREATE,
            element: (
              <GuardRoute requirePermissions={[AclPermission.databaseThresholdConfigCreate]}>
                <CreateOrUpdateDatabaseThresholdConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.databaseThresholdConfigView,
                  AclPermission.databaseThresholdConfigEdit,
                  AclPermission.databaseThresholdConfigCreate,
                ]}>
                <CreateOrUpdateDatabaseThresholdConfigPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.VARIABLE,
        element: (
          <GuardRoute requirePermissions={[AclPermission.variableView]}>
            <VariablePage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.EXECUTION_CONFIG,
        element: (
          <GuardRoute requirePermissions={[AclPermission.executionView, AclPermission.executionGroupView]}>
            <ExecutionConfigPage />
          </GuardRoute>
        ),
      },
      {
        path: ROUTE_PATH.EXECUTION_HISTORY,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.executionHistoryView]}>
                <ExecutionHistoryPage />
              </GuardRoute>
            ),
          },
          {
            path: ':executionHistoryId',
            element: (
              <GuardRoute requirePermissions={[AclPermission.executionHistoryView]}>
                <ExecutionHistoryDetailPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.AUTO_TRIGGER_ACTION_CONFIGS,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.autoTriggerActionConfigView]}>
                <AutoTriggerActionConfigPage />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.autoTriggerActionConfigView,
                  AclPermission.autoTriggerActionConfigEdit,
                  AclPermission.autoTriggerActionConfigCreate,
                ]}>
                <AutoTriggerActionConfigFormPage />
              </GuardRoute>
            ),
          },
        ],
      },
      {
        path: ROUTE_PATH.NOTIFICATION_EVENT,
        children: [
          {
            index: true,
            element: (
              <GuardRoute requirePermissions={[AclPermission.notificationEventView]}>
                <NotificationEventPage />
              </GuardRoute>
            ),
          },
          {
            path: ':id',
            element: (
              <GuardRoute
                requirePermissions={[
                  AclPermission.notificationEventView,
                  AclPermission.notificationEventEdit,
                  AclPermission.notificationEventCreate,
                ]}>
                <NotificationEventDetail />
              </GuardRoute>
            ),
          },
        ],
      },
    ],
  },
  {
    path: ROUTE_PATH.REPORT,
    element: (
      <GuardRoute requirePermissions={[AclPermission.reportView]}>
        <ReportViewPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.EXPORT_DATA,
    element: <ManagementExportDataPage />,
  },
  {
    path: ROUTE_PATH.MONITOR_ALERT,
    element: (
      <GuardRoute requirePermissions={[AclPermission.monitorAlertView]}>
        <MonitorAlertPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.EVENT,
    children: [
      {
        index: true,
        element: (
          <GuardRoute requirePermissions={[AclPermission.taskView]}>
            <EventPage />
          </GuardRoute>
        ),
      },
      {
        path: ':taskId',
        element: (
          <GuardRoute requirePermissions={[AclPermission.taskView, AclPermission.taskEdit]}>
            <CreateOrUpdateShiftHandoverPage />
          </GuardRoute>
        ),
      },
    ],
  },
  {
    path: '/dashboard',
    element: <DashboardPage />,
  },
  {
    path: '/develop-guide/design-system',
    element: <DesignSystemPage />,
  },
  {
    path: '/develop-guide/use-context',
    element: <DemoUseContextPage />,
  },
  {
    path: '/develop-guide/demo-forbidden',
    element: (
      <GuardRoute requirePermissions={[AclPermission.webHookView]}>
        <DemoForbiddenPage />
      </GuardRoute>
    ),
  },
  {
    path: '/develop-guide/demo-forbidden-component',
    element: <DemoForbiddenComponentPage />,
  },
  {
    path: ROUTE_PATH.EXECUTION,
    element: (
      <GuardRoute requirePermissions={[AclPermission.anyExecution]}>
        <ExecutionPage />
      </GuardRoute>
    ),
  },
];
