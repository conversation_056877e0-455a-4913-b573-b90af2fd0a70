import { Box, Flex } from '@mantine/core';
import React, { useCallback, useEffect, useMemo } from 'react';
import DragTable from '@components/dragTable/DragTable';
import { Column, OnDragHandler } from '@components/dragTable/Types';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { WebMonitorConfigModel } from '@models/WebMonitorConfigModel';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanSelect, KanbanTooltip } from 'kanban-design-system';
import { ACTION_TYPE_LABEL, ActionTypeEnum, FIND_ELEMENT_BY_LABEL, FindElementByEnum } from '@common/constants/WebMonitorConfigConstant';
import { Controller, FieldArrayWithId, useFieldArray, UseFormReturn, useWatch } from 'react-hook-form';
import { IconPlus, IconTrash } from '@tabler/icons-react';

interface Props {
  form: UseFormReturn<WebMonitorConfigModel>;
  isViewMode?: boolean;
}

type FieldType = FieldArrayWithId<WebMonitorConfigModel, 'actions', 'fieldId'>;
const ENABLE_TYPE = [ActionTypeEnum.SEND_KEY, ActionTypeEnum.SELECT_FROM_DROPDOWN, ActionTypeEnum.WAIT, ActionTypeEnum.WAITING_FOR_ELEMENT];

const ActionSession = ({ form, isViewMode }: Props) => {
  const { control } = form;

  const { fields, insert, move, remove } = useFieldArray({
    control,
    name: 'actions',
    keyName: 'fieldId',
  });

  const watchedActionList = useWatch({
    control,
    name: 'actions',
  });

  useEffect(() => {
    watchedActionList?.forEach((action, index) => {
      const type = action.actionType;

      const shouldClearValue =
        type !== ActionTypeEnum.SEND_KEY &&
        type !== ActionTypeEnum.SELECT_FROM_DROPDOWN &&
        type !== ActionTypeEnum.WAIT &&
        type !== ActionTypeEnum.WAITING_FOR_ELEMENT;

      const shouldClearIdentifier = type === ActionTypeEnum.WAIT;

      if (shouldClearValue && action.value) {
        form.setValue(`actions.${index}.value`, '', {
          shouldDirty: true,
          shouldValidate: false,
        });
      }

      if (shouldClearIdentifier && action.identifier) {
        form.setValue(`actions.${index}.identifier`, '', {
          shouldDirty: true,
          shouldValidate: false,
        });
      }
    });
  }, [watchedActionList, form]);

  const columns = useMemo<Column<FieldType>[]>(
    () => [
      {
        id: 'actionType',
        title: 'Action Type',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          return (
            <Controller
              name={`actions.${index}.actionType`}
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  {...field}
                  disabled={isViewMode}
                  required
                  data={Object.values(ActionTypeEnum).map((key) => ({ value: key, label: ACTION_TYPE_LABEL[key] }))}
                  defaultValue={record?.actionType ?? ActionTypeEnum.CLICK}
                  allowDeselect={false}
                  searchable={true}
                  error={fieldState.error?.message}
                />
              )}
            />
          );
        },
        width: '18%',
      },
      {
        id: 'findElementBy',
        title: 'Find Element By',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          return (
            <Controller
              name={`actions.${index}.findElementBy`}
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  {...field}
                  disabled={isViewMode}
                  required
                  data={Object.values(FindElementByEnum).map((key) => ({ value: key, label: FIND_ELEMENT_BY_LABEL[key] }))}
                  defaultValue={record?.findElementBy ?? FindElementByEnum.XPATH}
                  allowDeselect={false}
                  error={fieldState.error?.message}
                />
              )}
            />
          );
        },
        width: '20%',
      },
      {
        id: 'identifier',
        title: 'Identifier',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          const currentActionType = watchedActionList?.[index]?.actionType;
          const shouldDisable = [
            ActionTypeEnum.WAIT,
            ActionTypeEnum.SWITCH_TO_POPUP,
            ActionTypeEnum.CLOSE_POPUP,
            ActionTypeEnum.BACK_TO_MAIN,
          ].includes(currentActionType);
          return (
            <Controller
              name={`actions.${index}.identifier`}
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  {...field}
                  disabled={isViewMode || shouldDisable}
                  required={!shouldDisable}
                  maxLength={5000}
                  error={fieldState.error?.message}
                />
              )}
            />
          );
        },
        width: '25%',
      },
      {
        id: 'value',
        title: 'Value',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          const currentActionType = watchedActionList?.[index]?.actionType;
          const shouldEnable = ENABLE_TYPE.includes(currentActionType);
          return (
            <Controller
              name={`actions.${index}.value`}
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  {...field}
                  disabled={isViewMode || !shouldEnable} // default is disabled
                  required={shouldEnable}
                  maxLength={500}
                  error={fieldState.error?.message}
                />
              )}
            />
          );
        },
        width: '25%',
      },
      {
        id: 'actions',
        title: '',
        render: (record) => (
          <KanbanTooltip label='Delete'>
            <KanbanIconButton
              variant='transparent'
              size={'sm'}
              disabled={isViewMode || fields.length === 1}
              onClick={() => {
                const index = fields.findIndex((item) => item.fieldId === record.fieldId);
                if (index !== -1) {
                  remove(index);
                }
              }}>
              <IconTrash color='red' />
            </KanbanIconButton>
          </KanbanTooltip>
        ),
        width: '5%',
      },
    ],
    [isViewMode, remove, fields, control, watchedActionList],
  );

  const updatePositionHandler = useCallback<OnDragHandler<FieldType>>(
    (active, over) => {
      if (!active || !over || active.fieldId === over.fieldId) {
        return;
      }

      const fromIndex = fields.findIndex((item) => item.fieldId === active.fieldId);
      const toIndex = fields.findIndex((item) => item.fieldId === over.fieldId);

      if (fromIndex === -1 || toIndex === -1) {
        return;
      }

      move(fromIndex, toIndex);
    },
    [fields, move],
  );

  const handleAddAction = () => {
    insert(fields.length, {
      actionType: ActionTypeEnum.CLICK,
      findElementBy: FindElementByEnum.XPATH,
      identifier: '',
      value: '',
    });
  };

  return (
    <Box>
      <DragTable
        disableDraggable={!isAnyPermissions([AclPermission.webMonitorConfigEdit])}
        columns={columns}
        data={fields}
        onDragHandler={updatePositionHandler}
        showIndexColumn={true}
        dataKey={(record) => record.fieldId}
      />
      {!isViewMode && (
        <Flex direction='row' gap='xs' align='center'>
          <KanbanButton size='xs' disabled={isViewMode} onClick={handleAddAction} leftSection={<IconPlus />}>
            Add Action
          </KanbanButton>
        </Flex>
      )}
    </Box>
  );
};

export default ActionSession;
