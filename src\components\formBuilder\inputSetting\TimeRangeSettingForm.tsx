import React from 'react';
import { Stack, Text, Checkbox, Paper, Title } from '@mantine/core';
import { KanbanInput } from 'kanban-design-system';
import { Control, Controller } from 'react-hook-form';
import { TimeRangeElement } from '../Types';

interface Props {
  control: Control<TimeRangeElement>;
}

const TimeRangeSettingForm = ({ control }: Props) => {
  return (
    <Stack gap='md'>
      {/* Label Field */}
      <div>
        <Text size='sm' fw={500} mb='xs'>
          Label
        </Text>
        <Controller name='label' control={control} render={({ field }) => <KanbanInput placeholder='Time Range' {...field} />} />
      </div>

      {/* Help Text Field */}
      <div>
        <Text size='sm' fw={500} mb='xs'>
          Help text
        </Text>
        <Controller name='helpText' control={control} render={({ field }) => <KanbanInput placeholder='Enter help text' {...field} />} />
      </div>

      {/* From Date Settings */}
      <Paper p='md' withBorder>
        <Title order={6} mb='md'>
          From Date Settings
        </Title>
        <Stack gap='md'>
          {/* From Date Label */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              From Date Label
            </Text>
            <Controller name='fromDateLabel' control={control} render={({ field }) => <KanbanInput placeholder='Start Date' {...field} />} />
          </div>

          {/* From Date Placeholder */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              From Date Placeholder
            </Text>
            <Controller
              name='fromDatePlaceholder'
              control={control}
              render={({ field }) => <KanbanInput placeholder='Select start date' {...field} />}
            />
          </div>

          {/* From Date Default Value */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              From Date Default Value
            </Text>
            <Controller name='fromDate' control={control} render={({ field }) => <KanbanInput placeholder='YYYY-MM-DD' {...field} />} />
          </div>

          {/* From Date Required */}
          <div>
            <Controller
              name='fromDateRequired'
              control={control}
              render={({ field }) => (
                <Checkbox label='From Date Required' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />
              )}
            />
          </div>
        </Stack>
      </Paper>

      {/* To Date Settings */}
      <Paper p='md' withBorder>
        <Title order={6} mb='md'>
          To Date Settings
        </Title>
        <Stack gap='md'>
          {/* To Date Label */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              To Date Label
            </Text>
            <Controller name='toDateLabel' control={control} render={({ field }) => <KanbanInput placeholder='End Date' {...field} />} />
          </div>

          {/* To Date Placeholder */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              To Date Placeholder
            </Text>
            <Controller name='toDatePlaceholder' control={control} render={({ field }) => <KanbanInput placeholder='Select end date' {...field} />} />
          </div>

          {/* To Date Default Value */}
          <div>
            <Text size='sm' fw={500} mb='xs'>
              To Date Default Value
            </Text>
            <Controller name='toDate' control={control} render={({ field }) => <KanbanInput placeholder='YYYY-MM-DD' {...field} />} />
          </div>

          {/* To Date Required */}
          <div>
            <Controller
              name='toDateRequired'
              control={control}
              render={({ field }) => (
                <Checkbox label='To Date Required' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />
              )}
            />
          </div>
        </Stack>
      </Paper>

      {/* Overall Requirement */}
      <div>
        <Controller
          name='required'
          control={control}
          render={({ field }) => (
            <Checkbox label='Overall Requirement' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />
          )}
        />
      </div>
    </Stack>
  );
};

export default TimeRangeSettingForm;
