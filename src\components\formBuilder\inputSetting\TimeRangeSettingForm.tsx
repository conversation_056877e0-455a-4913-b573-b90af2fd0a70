import React from 'react';
import { Stack, Checkbox, Tabs } from '@mantine/core';
import { KanbanInput } from 'kanban-design-system';
import { Control, Controller } from 'react-hook-form';
import { TimeRangeElement } from '../Types';

interface Props {
  control: Control<TimeRangeElement>;
}

const TimeRangeSettingForm = ({ control }: Props) => {
  return (
    <Stack gap='md'>
      <Controller name='label' control={control} render={({ field }) => <KanbanInput label='Label' placeholder='Time Range' {...field} />} />

      <Controller
        name='helpText'
        control={control}
        render={({ field }) => <KanbanInput label='Help text' placeholder='Enter help text' {...field} />}
      />

      <Tabs defaultValue='fromDate'>
        <Tabs.List>
          <Tabs.Tab value='fromDate'>From Date</Tabs.Tab>
          <Tabs.Tab value='toDate'>To Date</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value='fromDate' pt='md'>
          <Stack gap='md'>
            <Controller
              name='fromDateLabel'
              control={control}
              render={({ field }) => <KanbanInput label='From Date Label' placeholder='Start Date' {...field} />}
            />

            <Controller
              name='fromDatePlaceholder'
              control={control}
              render={({ field }) => <KanbanInput label='From Date Placeholder' placeholder='Select start date' {...field} />}
            />
            {/* <div>
              <Text size='sm' fw={500} mb='xs'>
                From Date Default Value
              </Text>
              <Controller name='fromDate' control={control} render={({ field }) => <KanbanInput placeholder='YYYY-MM-DD' {...field} />} />
            </div> */}

            <Controller
              name='fromDateRequired'
              control={control}
              render={({ field }) => (
                <Checkbox label='From Date Required' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />
              )}
            />
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value='toDate' pt='md'>
          <Stack gap='md'>
            <Controller
              name='toDateLabel'
              control={control}
              render={({ field }) => <KanbanInput label='To Date Label' placeholder='End Date' {...field} />}
            />

            <Controller
              name='toDatePlaceholder'
              control={control}
              render={({ field }) => <KanbanInput label='To Date Placeholder' placeholder='Select end date' {...field} />}
            />

            {/* <div>
              <Text size='sm' fw={500} mb='xs'>
                To Date Default Value
              </Text>
              <Controller name='toDate' control={control} render={({ field }) => <KanbanInput label='To Date Default Value' placeholder='YYYY-MM-DD' {...field} />} />
            </div> */}

            <Controller
              name='toDateRequired'
              control={control}
              render={({ field }) => (
                <Checkbox label='To Date Required' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />
              )}
            />
          </Stack>
        </Tabs.Panel>
      </Tabs>
    </Stack>
  );
};

export default TimeRangeSettingForm;
