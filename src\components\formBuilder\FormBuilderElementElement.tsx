import { useDraggable } from '@dnd-kit/core';
import { useMemo } from 'react';
import { Box, Flex, Text } from '@mantine/core';
import styles from './FormBuilder.module.css';
import { type FormBuilderElement } from './Types';
import React from 'react';

const FormBuilderElementElement = ({ element, icon }: { element: FormBuilderElement; icon: React.ReactNode }) => {
  const { attributes, isDragging, listeners, setNodeRef } = useDraggable({
    id: element.id,
    data: {
      type: element.type,
      isNew: true,
      element,
    },
  });

  const style = useMemo(
    () => ({
      transform: isDragging ? 'scale(0.95)' : 'none',
      opacity: isDragging ? 0.6 : 1,
      border: `1px dashed var(--mantine-color-gray-4)`,
      cursor: isDragging ? 'grabbing' : 'grab',
      borderRadius: 'var(--mantine-radius-md)',
    }),
    [isDragging],
  );
  return (
    <Box ref={setNodeRef} {...attributes} {...listeners} style={style}>
      <Flex key={element.id} align='center' justify='flex-start' gap='md' className={styles.formBuilderElementListItem}>
        {icon}
        <Text size='md' fw={500}>
          {element.label}
        </Text>
      </Flex>
    </Box>
  );
};

export default FormBuilderElementElement;
