import { NotificationSourceTypeEnum, NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { z } from 'zod';
import { createDateTimeSchema } from './Common';
import { DATE_FORMAT } from '@common/constants/DateConstants';

export const NotificationSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  type: z.nativeEnum(NotificationTypeEnum),
  userName: z.string(),
  sourceId: z.string(),
  sourceType: z.nativeEnum(NotificationSourceTypeEnum),
  isRead: z.boolean(),
  readDate: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS).optional(),
  createdDate: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
});

export type Notification = z.infer<typeof NotificationSchema>;
