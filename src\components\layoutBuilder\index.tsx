import React from 'react';
import { Container, Title, Text } from '@mantine/core';
import LayoutBuilder from '@components/layoutBuilder/LayoutBuilder';

const LayoutBuilderPage: React.FC = () => {
  return (
    <Container fluid>
      <Title order={1}>Layout Builder Demo</Title>
      <Text c='dimmed' mb='lg'>
        Drag and drop components from the palette to build your layout. Sessions support 1, 2, or 3 column layouts and can contain other components.
      </Text>

      <LayoutBuilder />
    </Container>
  );
};

export default LayoutBuilderPage;
