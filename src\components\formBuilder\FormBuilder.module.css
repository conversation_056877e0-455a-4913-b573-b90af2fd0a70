.formBuilderElementList {
  background-color: var(--mantine-color-white);
  border-right: 1px solid var(--mantine-color-gray-3);
  height: 100%;
}
.formBuilderElementListItem {
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  border-radius: var(--mantine-radius-md);
  background-color: var(--mantine-color-white);
  transition: all 0.2s ease-in-out;
}
.formBuilderLayoutRender {
  height: 100%;
  width: 100%;
  overflow-y: auto;
}

.formBuilderSettingIcon {
  border: 1px solid var(--mantine-color-gray-6);
  box-shadow: var(--mantine-shadow-md);
  background-color: white;
}

.formBuilderElementRenderItem{
  position: absolute;
  top: 5;
  right: 10;
  box-shadow: var(--mantine-shadow-xl);
  border-radius: var(--mantine-radius-sm);
  border: 1px solid var(--mantine-color-gray-5);
  transition: opacity 0.5s ease;
  background-color: white;
  padding: calc(var(--mantine-spacing-xs) / 2);
}