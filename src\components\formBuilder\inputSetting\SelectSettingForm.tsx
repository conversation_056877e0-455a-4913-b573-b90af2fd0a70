import React from 'react';
import { Stack, TextInput, Button, ActionIcon, Paper, Checkbox, Flex, Box, Text } from '@mantine/core';
import { IconGripVertical, IconTrash, IconPlus } from '@tabler/icons-react';
import { KanbanInput } from 'kanban-design-system';
import { Control, Controller, useFieldArray } from 'react-hook-form';
import { SelectElement } from '../Types';

interface Props {
  control: Control<SelectElement>;
}

const SelectSettingForm = ({ control }: Props) => {
  const { append, fields, remove } = useFieldArray({
    control,
    name: 'options',
  });

  const addOption = () => {
    append({
      label: 'New Option',
      value: 'new_option',
    });
  };

  return (
    <Stack gap='md'>
      <Controller name='label' control={control} render={({ field }) => <KanbanInput label='Label' placeholder='Name' {...field} />} />
      <Controller
        name='helpText'
        control={control}
        render={({ field }) => <KanbanInput label='Help text' placeholder='Enter help text' {...field} />}
      />
      <Controller name='placeholder' control={control} render={({ field }) => <TextInput label='Placeholder' {...field} />} />
      <Box>
        <Text size='sm' fw={500} mb='xs'>
          Values
        </Text>
        <Stack gap='xs' bg='blue.0' p='xs' style={{ borderRadius: 'var(--mantine-radius-md)' }}>
          {fields.map((field, index) => (
            <Paper key={field.id} p='xs' withBorder>
              <Flex align='center' gap='xs'>
                <ActionIcon variant='subtle' size='sm'>
                  <IconGripVertical size={14} />
                </ActionIcon>

                <Controller
                  name={`options.${index}.label`}
                  control={control}
                  render={({ field }) => <TextInput flex={1} placeholder={index === 0 ? 'Select' : 'Option'} {...field} />}
                />

                {fields.length > 1 && (
                  <ActionIcon variant='subtle' color='red' size='sm' onClick={() => remove(index)}>
                    <IconTrash size={14} />
                  </ActionIcon>
                )}
              </Flex>
            </Paper>
          ))}

          <Button variant='subtle' leftSection={<IconPlus size={14} />} onClick={addOption} size='sm' style={{ alignSelf: 'flex-start' }}>
            Add option
          </Button>
        </Stack>
      </Box>

      <Controller
        name='required'
        control={control}
        render={({ field }) => <Checkbox label='Requirement' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />}
      />
    </Stack>
  );
};

export default SelectSettingForm;
