import {
  LayoutItem,
  LayoutItemType,
  SessionLayoutType,
  SessionLayoutItem,
  InputLayoutItem,
  TextLayoutItem,
  ButtonLayoutItem,
  SelectLayoutItem,
  CheckboxLayoutItem,
  TextareaLayoutItem,
} from './Types';

export const generateId = (): string => {
  return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const createNewItem = (type: LayoutItemType): LayoutItem => {
  const baseItem = {
    id: generateId(),
    type,
    label: `${type.charAt(0).toUpperCase() + type.slice(1)} Label`,
  };

  switch (type) {
    case LayoutItemType.SESSION:
      return {
        ...baseItem,
        type: LayoutItemType.SESSION,
        layoutType: SessionLayoutType.ONE_COLUMN,
        children: [[]],
        title: 'New Session',
      } as SessionLayoutItem;

    case LayoutItemType.INPUT:
      return {
        ...baseItem,
        type: LayoutItemType.INPUT,
        inputType: 'text',
        placeholder: 'Enter text...',
      } as InputLayoutItem;

    case LayoutItemType.TEXT:
      return {
        ...baseItem,
        type: LayoutItemType.TEXT,
        content: 'Sample text content',
        fontSize: '14px',
        fontWeight: 'normal',
        color: 'black',
      } as TextLayoutItem;

    case LayoutItemType.BUTTON:
      return {
        ...baseItem,
        type: LayoutItemType.BUTTON,
        variant: 'filled',
        color: 'blue',
      } as ButtonLayoutItem;

    case LayoutItemType.SELECT:
      return {
        ...baseItem,
        type: LayoutItemType.SELECT,
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
      } as SelectLayoutItem;

    case LayoutItemType.CHECKBOX:
      return {
        ...baseItem,
        type: LayoutItemType.CHECKBOX,
        checked: false,
      } as CheckboxLayoutItem;

    case LayoutItemType.TEXTAREA:
      return {
        ...baseItem,
        type: LayoutItemType.TEXTAREA,
        rows: 3,
        placeholder: 'Enter text...',
      } as TextareaLayoutItem;

    default:
      throw new Error(`Unknown item type: ${type}`);
  }
};

export const getSessionColumns = (layoutType: SessionLayoutType): number => {
  switch (layoutType) {
    case SessionLayoutType.ONE_COLUMN:
      return 1;
    case SessionLayoutType.TWO_COLUMN:
      return 2;
    case SessionLayoutType.THREE_COLUMN:
      return 3;
    default:
      return 1;
  }
};

export const initializeSessionChildren = (layoutType: SessionLayoutType): LayoutItem[][] => {
  const columns = getSessionColumns(layoutType);
  return Array(columns)
    .fill(null)
    .map(() => []);
};

export const findItemById = (items: LayoutItem[], id: string): LayoutItem | null => {
  for (const item of items) {
    if (item.id === id) {
      return item;
    }
    if (item.type === LayoutItemType.SESSION) {
      const sessionItem = item as SessionLayoutItem;
      for (const column of sessionItem.children) {
        const found = findItemById(column, id);
        if (found) {
          return found;
        }
      }
    }
  }
  return null;
};

export const removeItemById = (items: LayoutItem[], id: string): LayoutItem[] => {
  return items.filter((item) => {
    if (item.id === id) {
      return false;
    }
    if (item.type === LayoutItemType.SESSION) {
      const sessionItem = item as SessionLayoutItem;
      sessionItem.children = sessionItem.children.map((column) => removeItemById(column, id));
    }
    return true;
  });
};
