### Jira link
Provide link to Jira MBMONITOR (US, Issue)

### Merge type (check one)
- [ ] Feature
- [ ] Fix bug
- [ ] Other 

### Status (check one)
- [ ] Done
- [ ] In-progress

### Merge request checklists (check all)
- [ ] Code follows project coding guidelines.
- [ ] `npm start` have no warning or error.
- [ ] `npm run lint` have no warning or error.
- [ ] Naming Conventions (component, filename, etc)
- [ ] React Dependencies is valid
- [ ] No hard-coded variables
- [ ] The code is easy to read (Readability)
- [ ] Avoid duplicate code, component
