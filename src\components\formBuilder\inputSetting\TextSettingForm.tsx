import React from 'react';
import { Checkbox, Stack, TextInput } from '@mantine/core';
import { TextInputElement } from '../Types';
import { Control, Controller } from 'react-hook-form';

interface Props {
  control: Control<TextInputElement>;
}

const TextSettingForm = ({ control }: Props) => {
  return (
    <Stack gap='md'>
      <Controller name='label' control={control} render={({ field }) => <TextInput label='Label' {...field} />} />
      <Controller name='defaultValue' control={control} render={({ field }) => <TextInput label='Default value' {...field} />} />
      <Controller name='helpText' control={control} render={({ field }) => <TextInput label='Help text' {...field} />} />
      <Controller name='placeholder' control={control} render={({ field }) => <TextInput label='Placeholder' {...field} />} />
      <Controller name='maxLength' control={control} render={({ field }) => <TextInput label='Max length' {...field} />} />
      <Controller
        name='required'
        control={control}
        render={({ field }) => <Checkbox label='Required' checked={field.value} onChange={field.onChange} />}
      />
    </Stack>
  );
};

export default TextSettingForm;
