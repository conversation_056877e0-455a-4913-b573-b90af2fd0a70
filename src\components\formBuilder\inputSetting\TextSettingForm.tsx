import React, { useState } from 'react';
import { Stack, TextInput, Button, ActionIcon, Paper, Text, Checkbox, Flex } from '@mantine/core';
import { IconGripVertical, IconTrash, IconPlus } from '@tabler/icons-react';
import { KanbanInput } from 'kanban-design-system';
import { Control, Controller } from 'react-hook-form';

interface SelectOption {
  id: string;
  label: string;
  value: string;
}

interface Props {
  control: Control<any>;
}

const SelectSetting = ({ control }: Props) => {
  const [options, setOptions] = useState<SelectOption[]>([
    { id: '1', label: 'Select', value: 'select' },
    { id: '2', label: 'Option', value: 'option' },
  ]);

  const generateId = () => `option_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const addOption = () => {
    const newOption: SelectOption = {
      id: generateId(),
      label: 'New Option',
      value: 'new_option',
    };
    setOptions([...options, newOption]);
  };

  const updateOption = (id: string, field: 'label' | 'value', value: string) => {
    setOptions(options.map((option) => (option.id === id ? { ...option, [field]: value } : option)));
  };

  const removeOption = (id: string) => {
    if (options.length > 1) {
      setOptions(options.filter((option) => option.id !== id));
    }
  };

  return (
    <Stack gap='md'>
      {/* Label Field */}
      <div>
        <Text size='sm' fw={500} mb='xs'>
          Label
        </Text>
        <Controller name='label' control={control} render={({ field }) => <KanbanInput placeholder='Name' {...field} />} />
      </div>

      {/* Help Text Field */}
      <div>
        <Text size='sm' fw={500} mb='xs'>
          Help text
        </Text>
        <Controller name='helpText' control={control} render={({ field }) => <KanbanInput placeholder='Enter help text' {...field} />} />
      </div>

      {/* Values Section */}
      <div>
        <Text size='sm' fw={500} mb='xs'>
          Values
        </Text>
        <Stack gap='xs'>
          {options.map((option, index) => (
            <Paper key={option.id} p='xs' withBorder>
              <Flex align='center' gap='xs'>
                <ActionIcon variant='subtle' size='sm'>
                  <IconGripVertical size={14} />
                </ActionIcon>

                <TextInput
                  flex={1}
                  value={option.label}
                  onChange={(e) => updateOption(option.id, 'label', e.target.value)}
                  placeholder={index === 0 ? 'Select' : 'Option'}
                />

                {options.length > 1 && (
                  <ActionIcon variant='subtle' color='red' size='sm' onClick={() => removeOption(option.id)}>
                    <IconTrash size={14} />
                  </ActionIcon>
                )}
              </Flex>
            </Paper>
          ))}

          {/* Add Option Button */}
          <Button variant='subtle' leftSection={<IconPlus size={14} />} onClick={addOption} size='sm' style={{ alignSelf: 'flex-start' }}>
            Add option
          </Button>
        </Stack>
      </div>

      {/* Requirement Checkbox */}
      <div>
        <Controller
          name='required'
          control={control}
          render={({ field }) => <Checkbox label='Requirement' checked={field.value} onChange={(e) => field.onChange(e.currentTarget.checked)} />}
        />
      </div>
    </Stack>
  );
};

export default SelectSetting;
