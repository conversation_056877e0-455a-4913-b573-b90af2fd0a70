import { NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { NotificationEventScheduleTypeEnum } from '@common/constants/NotificationEventConstants';
import { MAX_NOTIFICATION_EVENT_CONTENT_JSON_LENGTH, MAX_NOTIFICATION_EVENT_TITLE_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const NotificationEventModelSchema = z
  .object({
    id: z.string().optional(),
    title: z.string().min(1).max(MAX_NOTIFICATION_EVENT_TITLE_LENGTH),
    content: z.string().min(1).max(MAX_NOTIFICATION_EVENT_CONTENT_JSON_LENGTH),
    notificationType: z.nativeEnum(NotificationTypeEnum),
    scheduleType: z.nativeEnum(NotificationEventScheduleTypeEnum),
    triggeredDate: z.string().optional(),
    cronExpression: z.string().optional(),
    userNames: z.array(z.string()),
    roleIds: z.array(z.string()),
  })
  .superRefine((value, ctx) => {
    if (value.userNames.length === 0 && value.roleIds.length === 0) {
      ['userNames', 'roleIds'].forEach((ele) =>
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: [ele],
          message: 'Please select at least one user or role',
        }),
      );
    }
    if (value.scheduleType === NotificationEventScheduleTypeEnum.CRON_EXPRESSION && !value.cronExpression) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['cronExpression'],
        message: 'Cron expression can not be empty',
      });
    }
    if (value.scheduleType === NotificationEventScheduleTypeEnum.ONE_TIME && !value.triggeredDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['triggeredDate'],
        message: 'Triggered date can not be empty',
      });
    }
  });

export type NotificationEventModel = z.infer<typeof NotificationEventModelSchema>;
