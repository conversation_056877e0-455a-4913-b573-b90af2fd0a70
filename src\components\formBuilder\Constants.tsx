import { IconCalendar, IconClearFormatting, IconLayoutGrid, IconNumber, IconSelect, IconTimeDuration0 } from '@tabler/icons-react';
import { IconCheckbox, IconRadio } from '@tabler/icons-react';
import React from 'react';
import { FormBuilderElement } from './Types';
import { EnumKey } from '@common/utils/Type';

export const SESSION_ROOT_ID = 'ROOT';

export enum FormBuilderElementTypeEnum {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  CHECKBOX = 'CHECKBOX',
  RADIO = 'RADIO',
  SELECT = 'SELECT',
  SESSION = 'SESSION',
  DATETIME = 'DATETIME',
  TIME_RANGE = 'TIME_RANGE',
}

export enum SessionLayoutEnum {
  ONE_COLUMN = 'ONE_COLUMN',
  TWO_COLUMN = 'TWO_COLUMN',
  THREE_COLUMN = 'THREE_COLUMN',
}

export const SessionLayoutInfo: EnumKey<SessionLayoutEnum, { col: number; label: string }> = {
  [SessionLayoutEnum.ONE_COLUMN]: {
    col: 1,
    label: '1 Column',
  },
  [SessionLayoutEnum.TWO_COLUMN]: {
    col: 2,
    label: '2 Column',
  },
  [SessionLayoutEnum.THREE_COLUMN]: {
    col: 3,
    label: '3 Column',
  },
};

export const DefaultFormBuilderElement: Record<FormBuilderElementTypeEnum, { element: FormBuilderElement; icon: React.ReactNode }> = {
  [FormBuilderElementTypeEnum.TEXT]: {
    element: {
      id: 'text-input',
      type: FormBuilderElementTypeEnum.TEXT,
      label: 'Text Input',
      required: false,
      helpText: '',
      placeholder: '',
      maxLength: 100,
      defaultValue: '',
    },
    icon: <IconClearFormatting size={24} />,
  },
  [FormBuilderElementTypeEnum.NUMBER]: {
    element: {
      id: 'number-input',
      type: FormBuilderElementTypeEnum.NUMBER,
      label: 'Number Input',
      required: false,
      helpText: '',
      placeholder: '',
      defaultValue: undefined,
      min: 0,
      max: 100,
    },
    icon: <IconNumber size={24} />,
  },
  [FormBuilderElementTypeEnum.CHECKBOX]: {
    element: {
      id: 'checkbox',
      type: FormBuilderElementTypeEnum.CHECKBOX,
      label: 'Checkbox',
      required: false,
      helpText: '',
    },
    icon: <IconCheckbox size={24} />,
  },
  [FormBuilderElementTypeEnum.RADIO]: {
    element: {
      id: 'radio',
      type: FormBuilderElementTypeEnum.RADIO,
      label: 'Radio',
      required: false,
      helpText: '',
    },
    icon: <IconRadio size={24} />,
  },
  [FormBuilderElementTypeEnum.SELECT]: {
    element: {
      id: 'select',
      type: FormBuilderElementTypeEnum.SELECT,
      label: 'Select',
      required: false,
      helpText: '',
      placeholder: '',
      options: [],
      multipleSelect: false,
      defaultValue: undefined,
    },
    icon: <IconSelect size={24} />,
  },
  [FormBuilderElementTypeEnum.SESSION]: {
    element: {
      id: 'session',
      type: FormBuilderElementTypeEnum.SESSION,
      label: 'Session',
      layout: SessionLayoutEnum.ONE_COLUMN,
      elements: [],
    },
    icon: <IconLayoutGrid size={24} />,
  },
  [FormBuilderElementTypeEnum.DATETIME]: {
    element: {
      id: 'datetime',
      type: FormBuilderElementTypeEnum.DATETIME,
      label: 'Date Time',
      required: false,
      helpText: '',
      placeholder: '',
    },
    icon: <IconCalendar size={24} />,
  },
  [FormBuilderElementTypeEnum.TIME_RANGE]: {
    element: {
      id: 'time-range',
      type: FormBuilderElementTypeEnum.TIME_RANGE,
      label: 'Time Range',
      required: false,
      helpText: '',
      fromDatePlaceholder: '',
      toDatePlaceholder: '',
      fromDate: '',
      toDate: '',
      fromDateRequired: false,
      toDateRequired: false,
      fromDateLabel: 'From date',
      toDateLabel: 'To date',
    },
    icon: <IconTimeDuration0 size={24} />,
  },
};
