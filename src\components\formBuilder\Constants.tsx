import { IconClearFormatting, IconLayoutGrid, IconNumber, IconSelect } from '@tabler/icons-react';
import { IconCheckbox, IconRadio } from '@tabler/icons-react';
import { type FormBuilderElement } from './Types';
import React from 'react';

export const SESSION_ROOT_ID = 'ROOT';

export enum FormBuilderElementTypeEnum {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  CHECKBOX = 'CHECKBOX',
  RADIO = 'RADIO',
  SELECT = 'SELECT',
  SESSION = 'SESSION',
}

export enum SessionLayout {
  ONE_COLUMN = 1,
  TWO_COLUMN = 2,
  THREE_COLUMN = 3,
}

export const DefaultFormBuilderElement: Record<FormBuilderElementTypeEnum, { element: FormBuilderElement; icon: React.ReactNode }> = {
  [FormBuilderElementTypeEnum.TEXT]: {
    element: {
      id: 'text-input',
      type: FormBuilderElementTypeEnum.TEXT,
      label: 'Text Input',
      required: false,
      helpText: '',
      placeholder: '',
      maxLength: 100,
    },
    icon: <IconClearFormatting size={24} />,
  },
  [FormBuilderElementTypeEnum.NUMBER]: {
    element: {
      id: 'number-input',
      type: FormBuilderElementTypeEnum.NUMBER,
      label: 'Number Input',
      required: false,
      helpText: '',
      placeholder: '',
    },
    icon: <IconNumber size={24} />,
  },
  [FormBuilderElementTypeEnum.CHECKBOX]: {
    element: {
      id: 'checkbox',
      type: FormBuilderElementTypeEnum.CHECKBOX,
      label: 'Checkbox',
      required: false,
      helpText: '',
    },
    icon: <IconCheckbox size={24} />,
  },
  [FormBuilderElementTypeEnum.RADIO]: {
    element: {
      id: 'radio',
      type: FormBuilderElementTypeEnum.RADIO,
      label: 'Radio',
      required: false,
      helpText: '',
    },
    icon: <IconRadio size={24} />,
  },
  [FormBuilderElementTypeEnum.SELECT]: {
    element: {
      id: 'select',
      type: FormBuilderElementTypeEnum.SELECT,
      label: 'Select',
      required: false,
      helpText: '',
      placeholder: '',
      options: [],
      multipleSelect: false,
    },
    icon: <IconSelect size={24} />,
  },
  [FormBuilderElementTypeEnum.SESSION]: {
    element: {
      id: 'session',
      type: FormBuilderElementTypeEnum.SESSION,
      label: 'Session',
      layout: SessionLayout.ONE_COLUMN,
      elements: [],
    },
    icon: <IconLayoutGrid size={24} />,
  },
};
