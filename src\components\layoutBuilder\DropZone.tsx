import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Box, Text, Paper } from '@mantine/core';

interface DropZoneProps {
  id: string;
  children?: React.ReactNode;
  isEmpty?: boolean;
  minHeight?: number;
}

const DropZone: React.FC<DropZoneProps> = ({ children, id, isEmpty = false, minHeight = 100 }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <Paper
      ref={setNodeRef}
      p='sm'
      withBorder
      style={{
        minHeight,
        backgroundColor: isOver ? 'var(--mantine-color-blue-0)' : isEmpty ? 'var(--mantine-color-gray-0)' : 'transparent',
        borderColor: isOver ? 'var(--mantine-color-blue-4)' : isEmpty ? 'var(--mantine-color-gray-3)' : 'var(--mantine-color-gray-2)',
        borderStyle: isEmpty ? 'dashed' : 'solid',
        transition: 'all 0.2s ease',
      }}>
      {isEmpty ? (
        <Box
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: minHeight - 32, // Account for padding
          }}>
          <Text c='dimmed' size='sm'>
            Drop items here
          </Text>
        </Box>
      ) : (
        children
      )}
    </Paper>
  );
};

export default DropZone;
