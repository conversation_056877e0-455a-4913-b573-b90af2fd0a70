import React, { useEffect } from 'react';
import { RichTextEditor } from '@mantine/tiptap';
import { NotificationEventModel } from '@models/NotificationEventModel';
import Link from '@tiptap/extension-link';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useController, UseFormReturn } from 'react-hook-form';
import { MAX_NOTIFICATION_EVENT_CONTENT_LENGTH } from '@common/constants/ValidationConstant';
import { safeJsonParse } from '@common/utils/CommonUtils';
import { Input } from '@mantine/core';

interface Props {
  form: UseFormReturn<NotificationEventModel>;
  disabled?: boolean;
}

const CustomLink = Link.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      style: {
        default: 'text-decoration: underline; color: var(--mantine-color-blue-7);',
      },
    };
  },
});

const NotificationEventContent = ({ disabled, form }: Props) => {
  const { field } = useController({ name: 'content', control: form.control });
  const editor = useEditor({
    extensions: [StarterKit, CustomLink],
    onUpdate: ({ editor }) => {
      const text = editor.getText();
      if (text.length <= MAX_NOTIFICATION_EVENT_CONTENT_LENGTH) {
        field.onChange(JSON.stringify(editor.getJSON()));
      } else {
        editor.commands.undo();
      }
    },
    editable: !disabled,
  });

  useEffect(() => {
    if (editor && field.value !== editor.getHTML()) {
      editor.commands.setContent(safeJsonParse(field.value) || undefined, false);
    }
  }, [field.value, editor]);
  return (
    <Input.Wrapper label='Notify Content' required>
      <RichTextEditor editor={editor} style={{ fontSize: 14 }}>
        <RichTextEditor.Toolbar p='calc(var(--mantine-spacing-xs) / 2)'>
          <RichTextEditor.Link />
        </RichTextEditor.Toolbar>
        <RichTextEditor.Content />
      </RichTextEditor>
    </Input.Wrapper>
  );
};

export default NotificationEventContent;
