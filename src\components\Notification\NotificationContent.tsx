import React from 'react';
import { JSONContent } from '@tiptap/react';
import { safeJsonParse } from '@common/utils/CommonUtils';

interface Props {
  content: string;
}

function renderTiptapJsonToText(node: JSONContent | undefined, keyPrefix = 'k'): React.ReactNode {
  if (!node) {
    return null;
  }

  switch (node.type) {
    case 'doc':
    case 'paragraph':
      return (
        <React.Fragment key={keyPrefix}>
          {node.content?.map((child, i) => renderTiptapJsonToText(child, `${keyPrefix}-${i}`))}
          {node.type === 'paragraph' && <br />}
        </React.Fragment>
      );

    case 'text': {
      const linkMark = node.marks?.find((mark) => mark.type === 'link');

      if (linkMark) {
        const { href } = linkMark.attrs || {};
        return (
          <a key={keyPrefix} href={href} target='_blank' rel='noopener noreferrer' style={{ color: 'blue', textDecoration: 'underline' }}>
            {node.text}
          </a>
        );
      }

      return <React.Fragment key={keyPrefix}>{node.text}</React.Fragment>;
    }

    default:
      // Optionally handle other block types like heading, bulletList, etc.
      return node.content?.map((child, i) => renderTiptapJsonToText(child, `${keyPrefix}-${i}`)) || null;
  }
}

const NotificationContent = ({ content }: Props) => {
  const contentJson = safeJsonParse(content);
  return <>{contentJson ? renderTiptapJsonToText(contentJson) : content}</>;
};

export default NotificationContent;
