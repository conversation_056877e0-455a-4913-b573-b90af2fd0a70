import React from 'react';
import { Stack, Title, Group } from '@mantine/core';
import { LayoutItemType } from './Types';
import { createNewItem } from './Utils';
import DraggableItem from './DraggableItem';
import { IconLayoutGrid, IconTextSize, IconClick, IconSelector, IconCheckbox, IconTextarea, IconInput } from '@tabler/icons-react';

const ItemPalette: React.FC = () => {
  const itemTypes = [
    { type: LayoutItemType.SESSION, icon: IconLayoutGrid, label: 'Session' },
    { type: LayoutItemType.INPUT, icon: IconInput, label: 'Input' },
    { type: LayoutItemType.TEXT, icon: IconTextSize, label: 'Text' },
    { type: LayoutItemType.BUTTON, icon: IconClick, label: 'Button' },
    { type: LayoutItemType.SELECT, icon: IconSelector, label: 'Select' },
    { type: LayoutItemType.CHECKBOX, icon: IconCheckbox, label: 'Checkbox' },
    { type: LayoutItemType.TEXTAREA, icon: IconTextarea, label: 'Textarea' },
  ];

  return (
    <Stack gap='md'>
      <Title order={4}>Components</Title>
      <Stack gap='xs'>
        {itemTypes.map(({ icon: Icon, label, type }) => {
          const item = createNewItem(type);
          return (
            <DraggableItem key={type} item={item} isNew>
              <Group gap='xs'>
                <Icon size={16} />
                <span>{label}</span>
              </Group>
            </DraggableItem>
          );
        })}
      </Stack>
    </Stack>
  );
};

export default ItemPalette;
