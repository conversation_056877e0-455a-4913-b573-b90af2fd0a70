# Expires map
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/json           max;
    application/javascript     max;
    ~image/                    max;
}

server {
  listen 9001;
  add_header X-Frame-Options "SAMEORIGIN";
 
  location / {

  location ~* .*remoteEntry.js$ {
    add_header Last-Modified $date_gmt;
    add_header Cache-Control 'no-store, no-cache';
    if_modified_since off;
    expires off;
    etag off;
  }

  root /usr/share/nginx/html;
  index index.html index.htm;
  try_files $uri $uri/ /mbmonitor-frontend/index.html =404;
  }
  expires $expires;
  gzip on;                   
}
