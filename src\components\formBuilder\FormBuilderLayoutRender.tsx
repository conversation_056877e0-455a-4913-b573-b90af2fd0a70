import { IconLayout } from '@tabler/icons-react';
import { Box, Center, Stack, Text, Title } from '@mantine/core';
import DropableElement from './DropableElement';
import styles from './FormBuilder.module.css';
import { FormBuilderElement, OnDeleteElementFnc, OnUpdateElementFnc } from './Types';
import React, { useCallback } from 'react';
import FormBuilderElementRender from './FormBuilderElementSessionRender';
import { FormBuilderElementTypeEnum, SESSION_ROOT_ID } from './Constants';

interface Props {
  elements: FormBuilderElement[];
  setElements: (elements: FormBuilderElement[]) => void;
}

const FormBuilderLayoutRender = ({ elements, setElements }: Props) => {
  const onUpdateElement = useCallback<OnUpdateElementFnc>(
    (element) => {
      setElements(
        elements.map((ele) => {
          if (ele.id === element.id) {
            return element;
          }
          return ele;
        }),
      );
    },
    [elements, setElements],
  );

  const onDeleteElement = useCallback<OnDeleteElementFnc>(
    (elementId, parentElementId) => {
      if (parentElementId) {
        setElements(
          elements.map((ele) => {
            if (ele.id === parentElementId && ele.type === FormBuilderElementTypeEnum.SESSION) {
              return { ...ele, elements: ele.elements.filter((ele) => ele.id !== elementId) };
            }
            return ele;
          }),
        );
        return;
      }
      setElements(elements.filter((ele) => ele.id !== elementId));
    },
    [elements, setElements],
  );

  return (
    <Stack className={styles.formBuilderLayoutRender}>
      <DropableElement id={SESSION_ROOT_ID} type={FormBuilderElementTypeEnum.SESSION} withBorder={false}>
        <Box flex={1}>
          {elements.length === 0 && (
            <Center h='100%'>
              <Stack gap='md' align='center'>
                <IconLayout size={40} color='gray' />
                <Title order={4} c='gray.6'>
                  Start Building Your Form
                </Title>
                <Text c='gray.6'>Drag and drop elements to build your form</Text>
              </Stack>
            </Center>
          )}
          {elements.length !== 0 && (
            <Stack gap='md'>
              {elements.map((element) => (
                <FormBuilderElementRender key={element.id} element={element} onDeleteElement={onDeleteElement} onUpdateElement={onUpdateElement} />
              ))}
            </Stack>
          )}
        </Box>
      </DropableElement>
    </Stack>
  );
};

export default FormBuilderLayoutRender;
