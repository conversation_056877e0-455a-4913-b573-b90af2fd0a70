import { DndContext, type DragEndEvent, DragOverlay, type DragStartEvent, pointerWithin } from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { useState } from 'react';
import { Box, Flex, Stack } from '@mantine/core';
import styles from './FormBuilder.module.css';
import { DraggableData, DropableData, FormBuilderElement } from './Types';
import React from 'react';
import FormBuilderElementElement from './FormBuilderElementElement';
import FormBuilderLayoutRender from './FormBuilderLayoutRender';
import { DefaultFormBuilderElement, FormBuilderElementTypeEnum, SESSION_ROOT_ID } from './Constants';
import { v4 } from 'uuid';

interface Props {
  elements: FormBuilderElement[];
  setElements: (elements: FormBuilderElement[]) => void;
}

const FormBuilder = ({ elements, setElements }: Props) => {
  const [draggedElement, setDraggedElement] = useState<FormBuilderElement | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setDraggedElement(event.active.data.current?.element);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      return;
    }

    const overData = over.data.current as DropableData;
    const activeData = active.data.current as DraggableData;

    // console.info(overData, activeData);
    setDraggedElement(null);

    if (overData.type === FormBuilderElementTypeEnum.SESSION && over.id === SESSION_ROOT_ID) {
      setElements([...elements, { ...activeData.element, id: v4() }]);
      return;
    }

    if (overData.type === FormBuilderElementTypeEnum.SESSION) {
      if (activeData.type === FormBuilderElementTypeEnum.SESSION) {
        return;
      }
      console.info(4);
      const sesionId = over.id;
      setElements([
        ...elements.map((ele) => {
          if (ele.id === sesionId && ele.type === FormBuilderElementTypeEnum.SESSION) {
            return { ...ele, elements: [...ele.elements, { ...activeData.element, id: v4() }] };
          }
          return ele;
        }),
      ]);
      return;
    }
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd} collisionDetection={pointerWithin}>
      <Flex style={{ height: '100%', width: '100%' }}>
        <Box style={{ width: '250px' }}>
          <Stack p='md' className={styles.formBuilderElementList}>
            {Object.values(DefaultFormBuilderElement).map((elementInfo) => {
              return <FormBuilderElementElement key={elementInfo.element.id} element={elementInfo.element} icon={elementInfo.icon} />;
            })}
          </Stack>
        </Box>
        <Box style={{ flex: 1 }} h='100%'>
          <FormBuilderLayoutRender elements={elements} setElements={setElements} />
        </Box>
      </Flex>

      {/* Drag Overlay */}
      <DragOverlay modifiers={[restrictToWindowEdges]}>
        {draggedElement && <FormBuilderElementElement element={draggedElement} icon={DefaultFormBuilderElement[draggedElement.type].icon} />}
      </DragOverlay>
    </DndContext>
  );
};

export default FormBuilder;
