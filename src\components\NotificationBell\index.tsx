import React, { useState, useEffect, useCallback } from 'react';
import { ActionIcon, Indicator, Popover } from '@mantine/core';
import { NotificationApi } from '@api/NotificationApi';
import useFetch from '@core/hooks/useFetch';
import { useDisclosure } from '@mantine/hooks';
import { IconBell } from '@tabler/icons-react';
import classes from './NotificationBell.module.css';
import NotificationPopup from './NotificationPopup';

interface NotificationBellProps {
  onClick?: () => void;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ onClick }) => {
  const [count, setCount] = useState(0);
  const [opened, { close, open, toggle }] = useDisclosure(false);

  const { data: unreadCountData } = useFetch(NotificationApi.getUnreadCount(), {
    showLoading: false,
  });

  useEffect(() => {
    if (unreadCountData?.data !== undefined) {
      setCount(unreadCountData.data);
    }
  }, [unreadCountData]);

  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
      event.stopPropagation();
      toggle();
      if (onClick) {
        onClick();
      }
    },
    [onClick, toggle],
  );

  return (
    <Popover
      width={350}
      position='bottom-end'
      withArrow
      shadow='lg'
      opened={opened}
      onChange={(opened) => (opened ? open() : close())}
      keepMounted={false}
      classNames={{
        dropdown: classes.notificationDropdown,
      }}>
      <Popover.Target>
        <Indicator
          disabled={count === 0}
          label={count > 99 ? '99+' : count.toString()}
          size={17}
          color='red.6'
          offset={5}
          processing
          classNames={{
            indicator: classes.bellIndicator,
          }}>
          <ActionIcon
            variant='light'
            size='lg'
            radius='xl'
            onClick={handleClick}
            aria-label={`Notifications (${count} unread)`}
            className={classes.bellButton}>
            <IconBell size='1.1rem' stroke={1.5} color='gray' />
          </ActionIcon>
        </Indicator>
      </Popover.Target>
      <Popover.Dropdown styles={{ dropdown: { boxShadow: 'var(--mantine-shadow-xl)' } }}>
        <NotificationPopup />
      </Popover.Dropdown>
    </Popover>
  );
};

export default NotificationBell;
