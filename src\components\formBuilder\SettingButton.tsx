import React from 'react';
import { FormBuilderElement, OnUpdateElementFnc } from './Types';
import { ActionIcon, Drawer } from '@mantine/core';
import classes from './FormBuilder.module.css';
import { IconSettings } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';

interface Props {
  element: FormBuilderElement;
  onUpdateElement: OnUpdateElementFnc;
}

const SettingButton = ({ element, onUpdateElement }: Props) => {
  const [opened, { close, open }] = useDisclosure(false);
  return (
    <>
      <Drawer opened={opened} onClose={close} title='Setting' position='right'>
        {/* Drawer content */}
      </Drawer>
      <ActionIcon variant='outline' size='md' className={classes.formBuilderSettingIcon} onClick={open}>
        <IconSettings color='var(--mantine-color-gray-6)' size={16} />
      </ActionIcon>
    </>
  );
};

export default SettingButton;
