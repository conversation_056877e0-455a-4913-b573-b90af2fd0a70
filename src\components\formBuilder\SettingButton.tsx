import React, { useEffect } from 'react';
import { FormBuilderElement, NumberInputElement, OnUpdateElementFnc, SelectElement, SessionElement, TextInputElement } from './Types';
import { ActionIcon, Drawer, Flex, Stack } from '@mantine/core';
import classes from './FormBuilder.module.css';
import { IconSettings } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { FormBuilderElementTypeEnum } from './Constants';
import TextSettingForm from './inputSetting/TextSettingForm';
import { Control, useForm } from 'react-hook-form';
import { KanbanButton } from 'kanban-design-system';
import NumberSettingForm from './inputSetting/NumberSettingForm';
import SelectSettingForm from './inputSetting/SelectSettingForm';
import SessionSettingForm from './inputSetting/SessionSettingForm';

interface Props {
  element: FormBuilderElement;
  onUpdateElement: OnUpdateElementFnc;
}

const SettingButton = ({ element, onUpdateElement }: Props) => {
  const [opened, { close, open }] = useDisclosure(false);

  const { control, getValues, reset } = useForm<FormBuilderElement>({
    defaultValues: element,
  });

  useEffect(() => {
    reset(element);
  }, [element, reset]);

  return (
    <>
      <Drawer
        opened={opened}
        onClose={close}
        title='Setting'
        styles={{
          title: {
            fontWeight: 600,
            fontSize: 'var(--mantine-font-size-md)',
          },
        }}
        position='right'>
        <Stack gap='md'>
          {element.type === FormBuilderElementTypeEnum.TEXT && <TextSettingForm control={control as Control<TextInputElement>} />}
          {element.type === FormBuilderElementTypeEnum.NUMBER && <NumberSettingForm control={control as Control<NumberInputElement>} />}
          {element.type === FormBuilderElementTypeEnum.SELECT && <SelectSettingForm control={control as Control<SelectElement>} />}
          {element.type === FormBuilderElementTypeEnum.SESSION && <SessionSettingForm control={control as Control<SessionElement>} />}
          <Flex gap='md' justify='flex-end'>
            <KanbanButton variant='outline' onClick={close}>
              Cancel
            </KanbanButton>
            <KanbanButton
              onClick={() => {
                onUpdateElement(getValues());
                close();
              }}>
              Save
            </KanbanButton>
          </Flex>
        </Stack>
      </Drawer>
      <ActionIcon variant='white' size='sm' className={classes.formBuilderSettingIcon} onClick={open}>
        <IconSettings color='var(--mantine-color-gray-6)' size={14} />
      </ActionIcon>
    </>
  );
};

export default SettingButton;
