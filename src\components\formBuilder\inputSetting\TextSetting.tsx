import React from 'react';
import { Box } from '@mantine/core';
import { TextInputElement } from '../Types';
import { useController } from 'react-hook-form';

interface Props {
  value: TextInputElement;
  onChange: (value: TextInputElement) => void;
}

const TextSetting = () => {
  const methods = useController();
  return (
    <Box>
      <TextInput label='Placeholder' value={value.placeholder} onChange={(event) => onChange({ ...value, placeholder: event.target.value })} />
    </Box>
  );
};

export default TextSetting;
