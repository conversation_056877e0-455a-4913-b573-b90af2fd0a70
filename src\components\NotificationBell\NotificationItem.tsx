import { Notification } from '@core/schema/Notification';
import { ActionIcon, Box, Group, Text } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
import React, { useCallback, useState } from 'react';
import { NotificationTypeInfo } from './Constants';
import classes from './NotificationBell.module.css';
import useMutate from '@core/hooks/useMutate';
import { NotificationApi } from '@api/NotificationApi';

interface Props {
  notification: Notification;
  onReadNotificationSuccess: (notification: Notification) => void;
}

const NotificationItem = ({ notification, onReadNotificationSuccess }: Props) => {
  const { content, createdDate, title, type } = notification;
  const [isRead, setIsRead] = useState(notification.isRead);
  const { mutate: readMutate } = useMutate(NotificationApi.markAsRead, {
    showLoading: false,
    onSuccess: () => {
      setIsRead(true);
      onReadNotificationSuccess(notification);
      console.info(notification.id, 'mark as read');
    },
  });

  const onClickNotification = useCallback(() => {
    if (notification.isRead) {
      return;
    }
    readMutate(notification.id);
  }, [notification.id, notification.isRead, readMutate]);
  return (
    <Group align='flex-start' gap='sm' p='xs' className={`${classes.notificationItem} ${isRead ? classes.isRead : ''}`} onClick={onClickNotification}>
      {NotificationTypeInfo[type].icon}
      <Box style={{ flex: 1 }}>
        <Group justify='space-between' align='flex-start'>
          <Text fw={600} size='sm' c={NotificationTypeInfo[type].color}>
            {title}
          </Text>
          <ActionIcon variant='subtle' size='xs'>
            <IconChevronDown size={12} />
          </ActionIcon>
        </Group>
        <Text size='xs' c='dimmed' mt={2}>
          {content}
        </Text>
        <Text size='xs' c='dimmed' mt={4}>
          {createdDate.fromNow()}
        </Text>
      </Box>
    </Group>
  );
};

export default NotificationItem;
