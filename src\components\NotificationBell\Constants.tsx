import React from 'react';
import { NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { IconAlertTriangle, IconBell, IconInfoCircle } from '@tabler/icons-react';
import { EnumKey } from '@common/utils/Type';

export const NotificationTypeInfo: EnumKey<NotificationTypeEnum, { label: string; color: string; icon: React.ReactNode }> = {
  [NotificationTypeEnum.INFO]: {
    label: 'Info',
    color: 'var(--mantine-color-blue-5)',
    icon: <IconBell size={20} color='var(--mantine-color-blue-5)' />,
  },
  [NotificationTypeEnum.WARNING]: {
    label: 'Warning',
    color: 'var(--mantine-color-yellow-5)',
    icon: <IconAlertTriangle size={20} color='var(--mantine-color-yellow-5)' />,
  },
  [NotificationTypeEnum.CRITICAL]: {
    label: 'Critical',
    color: 'var(--mantine-color-red-5)',
    icon: <IconInfoCircle size={20} color='var(--mantine-color-red-5)' />,
  },
};

export const DEFAULT_NOTIFICATION_PAGE_SIZE = 10;
