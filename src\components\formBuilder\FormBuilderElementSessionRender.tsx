import { useMemo, useState } from 'react';
import { ActionIcon, Box, Checkbox, Flex, NumberInput, Radio, Select, SimpleGrid, Stack, Text, TextInput, Title } from '@mantine/core';
import DropableElement from './DropableElement';
import { BaseFormBuilderElement, OnDeleteElementFnc, OnUpdateElementFnc, SessionElement, type FormBuilderElement } from './Types';
import React from 'react';
import { FormBuilderElementTypeEnum } from './Constants';
import { IconTrash } from '@tabler/icons-react';
import classes from './FormBuilder.module.css';
import SettingButton from './SettingButton';

interface Props<T extends BaseFormBuilderElement> {
  element: T;
  onUpdateElement: OnUpdateElementFnc;
  onDeleteElement: OnDeleteElementFnc;
}

const FormBuilderElementSessionRender = ({ element: session, onDeleteElement, onUpdateElement }: Props<SessionElement>) => {
  return (
    <DropableElement id={session.id} type={FormBuilderElementTypeEnum.SESSION} withBorder={true}>
      <Stack gap='md'>
        <Title order={4} style={{ textAlign: 'left' }}>
          {session.label}
        </Title>
        {session.elements.length === 0 && (
          <Flex
            w='100%'
            h='70px'
            align='center'
            justify='center'
            bg='var(--mantine-color-gray-0)'
            style={{ borderRadius: 'var(--mantine-radius-md)' }}>
            <Text c='gray.6'>Drop element here</Text>
          </Flex>
        )}
        <SimpleGrid cols={session.layout} spacing='lg'>
          {session.elements.map((element) => (
            <FormBuilderElementRender
              key={element.id}
              element={element as FormBuilderElement}
              onDeleteElement={() => onDeleteElement(element.id, session.id)}
              onUpdateElement={onUpdateElement}
            />
          ))}
        </SimpleGrid>
      </Stack>
    </DropableElement>
  );
};

const FormBuilderElementRender = ({ element, onDeleteElement, onUpdateElement }: Props<FormBuilderElement>) => {
  const [isHover, setIsHover] = useState(false);
  const renderElement = useMemo(() => {
    switch (element.type) {
      case FormBuilderElementTypeEnum.TEXT:
        return <TextInput label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.NUMBER:
        return <NumberInput label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.CHECKBOX:
        return <Checkbox label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.RADIO:
        return <Radio label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.SELECT:
        return <Select label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.SESSION:
        return <FormBuilderElementSessionRender element={element} onDeleteElement={onDeleteElement} onUpdateElement={onUpdateElement} />;
    }
  }, [element, onDeleteElement, onUpdateElement]);

  return (
    <Box
      style={{
        position: 'relative',
      }}
      onMouseOver={(event) => {
        event.stopPropagation();
        setIsHover(true);
      }}
      onMouseOut={(event) => {
        event.stopPropagation();
        setIsHover(false);
      }}>
      <Flex
        justify='center'
        style={{
          position: 'absolute',
          top: -10,
          right: 10,
          visibility: isHover ? 'visible' : 'hidden',
        }}
        gap='xs'>
        <SettingButton element={element} onUpdateElement={onUpdateElement} />
        <ActionIcon variant='outline' size='md' className={classes.formBuilderSettingIcon} onClick={() => onDeleteElement(element.id)}>
          <IconTrash color='var(--mantine-color-red-6)' size={16} />
        </ActionIcon>
      </Flex>
      {renderElement}
    </Box>
  );
};

export default FormBuilderElementRender;
