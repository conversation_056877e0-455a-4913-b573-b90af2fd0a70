import { useMemo, useState } from 'react';
import { ActionIcon, Box, Checkbox, Flex, NumberInput, Radio, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import DropableElement from './DropableElement';
import { BaseFormBuilderElement, OnDeleteElementFnc, OnUpdateElementFnc, SessionElement, TimeRangeElement, type FormBuilderElement } from './Types';
import React from 'react';
import { FormBuilderElementTypeEnum, SessionLayoutInfo } from './Constants';
import { IconDragDrop2, IconTrash } from '@tabler/icons-react';
import classes from './FormBuilder.module.css';
import SettingButton from './SettingButton';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { DateTimePicker } from '@mantine/dates';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface Props<T extends BaseFormBuilderElement> {
  element: T;
  onUpdateElement: OnUpdateElementFnc;
  onDeleteElement: OnDeleteElementFnc;
}

const FormBuilderElementSessionRender = ({ element: session, onDeleteElement, onUpdateElement }: Props<SessionElement>) => {
  return (
    <DropableElement id={session.id} type={FormBuilderElementTypeEnum.SESSION} withBorder={true}>
      <Stack gap='md'>
        <Title order={4} style={{ textAlign: 'left' }}>
          {session.label}
        </Title>
        {session.elements.length === 0 && (
          <Flex
            w='100%'
            h='70px'
            align='center'
            justify='center'
            bg='var(--mantine-color-gray-0)'
            style={{ borderRadius: 'var(--mantine-radius-md)', border: '1px dashed var(--mantine-color-gray-3)' }}>
            <Text c='gray.7'>Drop element here</Text>
          </Flex>
        )}
        <SimpleGrid cols={SessionLayoutInfo[session.layout].col} spacing='lg'>
          {session.elements.map((element) => (
            <FormBuilderElementRender
              key={element.id}
              element={element as FormBuilderElement}
              onDeleteElement={() => onDeleteElement(element.id, session.id)}
              onUpdateElement={onUpdateElement}
            />
          ))}
        </SimpleGrid>
      </Stack>
    </DropableElement>
  );
};

const TimeRangeRender = ({ element }: Props<TimeRangeElement>) => {
  return (
    <Flex gap='xs'>
      <DateTimePicker label='From date' style={{ flex: 1 }} />
      <DateTimePicker label='To data' style={{ flex: 1 }} />
    </Flex>
  );
};

const Sortable = ({ children, element, onDeleteElement, onUpdateElement }: Props<FormBuilderElement> & { children: React.ReactNode }) => {
  const [isHover, setIsHover] = useState(true);
  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: element.id,
    data: {
      type: element.type,
      isNew: false,
      element,
    },
  });

  return (
    <Box
      ref={setNodeRef}
      {...attributes}
      style={{
        position: 'relative',
        borderRadius: 'var(--mantine-radius-md)',
        border: '1px solid var(--mantine-color-gray-3)',
        transform: CSS.Translate.toString(transform),
        transition,
      }}
      p='md'
      bg={isDragging ? 'var(--mantine-color-blue-0)' : 'gray.0'}
      onMouseOver={(event) => {
        event.stopPropagation();
        setIsHover(true);
      }}
      onMouseOut={(event) => {
        event.stopPropagation();
        setIsHover(false);
      }}>
      <Flex
        justify='center'
        style={{
          opacity: isHover ? 1 : 0,
        }}
        className={classes.formBuilderElementRenderItem}
        gap='xs'>
        <ActionIcon
          variant='white'
          size='sm'
          className={classes.formBuilderSettingIcon}
          onClick={() => onDeleteElement && onDeleteElement(element.id)}
          {...listeners}>
          <IconDragDrop2 size={14} />
        </ActionIcon>
        {onUpdateElement && <SettingButton element={element} onUpdateElement={onUpdateElement} />}
        {onDeleteElement && (
          <ActionIcon variant='white' size='sm' className={classes.formBuilderSettingIcon} onClick={() => onDeleteElement(element.id)}>
            <IconTrash color='var(--mantine-color-red-6)' size={14} />
          </ActionIcon>
        )}
      </Flex>
      {children}
    </Box>
  );
};

const FormBuilderElementRender = ({ element, onDeleteElement, onUpdateElement }: Props<FormBuilderElement>) => {
  const renderElement = useMemo(() => {
    switch (element.type) {
      case FormBuilderElementTypeEnum.TEXT:
        return (
          <KanbanInput
            label={element.label}
            required={element.required}
            placeholder={element.placeholder}
            description={element.helpText}
            maxLength={element.maxLength}
            defaultValue={element.defaultValue}
          />
        );
      case FormBuilderElementTypeEnum.NUMBER:
        return (
          <NumberInput
            label={element.label}
            required={element.required}
            placeholder={element.placeholder}
            description={element.helpText}
            defaultValue={element.defaultValue}
            min={element.min}
            max={element.max}
          />
        );
      case FormBuilderElementTypeEnum.CHECKBOX:
        return <Checkbox label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.RADIO:
        return <Radio label={element.label} required={element.required} />;
      case FormBuilderElementTypeEnum.SELECT:
        return (
          <KanbanSelect
            label={element.label}
            required={element.required}
            data={element.options}
            placeholder={element.placeholder}
            description={element.helpText}
          />
        );
      case FormBuilderElementTypeEnum.SESSION:
        return <FormBuilderElementSessionRender element={element} onDeleteElement={onDeleteElement} onUpdateElement={onUpdateElement} />;
      case FormBuilderElementTypeEnum.DATETIME:
        return <DateTimePicker label={element.label} required={element.required} description={element.helpText} placeholder={element.placeholder} />;
      case FormBuilderElementTypeEnum.TIME_RANGE:
        return <TimeRangeRender element={element} onDeleteElement={onDeleteElement} onUpdateElement={onUpdateElement} />;
    }
  }, [element, onDeleteElement, onUpdateElement]);

  return (
    <Sortable element={element} onDeleteElement={onDeleteElement} onUpdateElement={onUpdateElement}>
      {renderElement}
    </Sortable>
  );
};

export default FormBuilderElementRender;
