import React from 'react';
import { Grid, Paper, Title, Group, ActionIcon, Stack } from '@mantine/core';
import { SessionLayoutItem, SessionLayoutType, LayoutItem } from './Types';
import { IconTrash, IconColumns1, IconColumns2, IconColumns3 } from '@tabler/icons-react';
import DropZone from './DropZone';
import LayoutItemRenderer from './LayoutItemRenderer';

interface SessionComponentProps {
  session: SessionLayoutItem;
  onUpdateSession: (session: SessionLayoutItem) => void;
  onDeleteSession: (sessionId: string) => void;
}

const SessionComponent: React.FC<SessionComponentProps> = ({ onDeleteSession, onUpdateSession, session }) => {
  const getColumnSpan = () => {
    switch (session.layoutType) {
      case SessionLayoutType.ONE_COLUMN:
        return 12;
      case SessionLayoutType.TWO_COLUMN:
        return 6;
      case SessionLayoutType.THREE_COLUMN:
        return 4;
      default:
        return 12;
    }
  };

  const changeLayoutType = (layoutType: SessionLayoutType) => {
    const newChildren: LayoutItem[][] = [];
    const columns = layoutType === SessionLayoutType.ONE_COLUMN ? 1 : layoutType === SessionLayoutType.TWO_COLUMN ? 2 : 3;

    // Initialize empty columns
    for (let i = 0; i < columns; i++) {
      newChildren.push([]);
    }

    // Redistribute existing items across new columns
    const allItems = session.children.flat();
    allItems.forEach((item, index) => {
      const columnIndex = index % columns;
      newChildren[columnIndex].push(item);
    });

    onUpdateSession({
      ...session,
      layoutType,
      children: newChildren,
    });
  };

  const getLayoutIcon = (type: SessionLayoutType) => {
    switch (type) {
      case SessionLayoutType.ONE_COLUMN:
        return IconColumns1;
      case SessionLayoutType.TWO_COLUMN:
        return IconColumns2;
      case SessionLayoutType.THREE_COLUMN:
        return IconColumns3;
    }
  };

  return (
    <Paper p='md' withBorder>
      <Group justify='space-between' mb='md'>
        <Title order={5}>{session.title}</Title>
        <Group gap='xs'>
          {[SessionLayoutType.ONE_COLUMN, SessionLayoutType.TWO_COLUMN, SessionLayoutType.THREE_COLUMN].map((type) => {
            const Icon = getLayoutIcon(type);
            return (
              <ActionIcon key={type} variant={session.layoutType === type ? 'filled' : 'outline'} size='sm' onClick={() => changeLayoutType(type)}>
                <Icon size={14} />
              </ActionIcon>
            );
          })}
          <ActionIcon variant='outline' size='sm' color='red' onClick={() => onDeleteSession(session.id)}>
            <IconTrash size={14} />
          </ActionIcon>
        </Group>
      </Group>

      <Grid>
        {session.children.map((column, columnIndex) => (
          <Grid.Col key={columnIndex} span={getColumnSpan()}>
            <DropZone id={`${session.id}-column-${columnIndex}`} isEmpty={column.length === 0} minHeight={150}>
              <Stack gap='sm'>
                {column.map((item) => (
                  <LayoutItemRenderer key={item.id} item={item} onUpdateSession={onUpdateSession} onDeleteSession={onDeleteSession} />
                ))}
              </Stack>
            </DropZone>
          </Grid.Col>
        ))}
      </Grid>
    </Paper>
  );
};

export default SessionComponent;
